{"name": "blazingtest", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "login": "node index.js login", "api": "node index.js api", "monitor": "node monitor.js", "orders": "node orders-monitor.js", "chat-links": "node chat-links.js", "chat-viewer": "node chat-viewer.js", "chat-web": "node chat-web.js", "pm2:start": "NODE_ENV=production pm2 start ecosystem.config.js", "pm2:start:dev": "pm2 start ecosystem.dev.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:stop:dev": "pm2 stop ecosystem.dev.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:restart:dev": "pm2 restart ecosystem.dev.config.js", "pm2:reload": "pm2 reload ecosystem.config.js", "pm2:reload:dev": "pm2 reload ecosystem.dev.config.js", "pm2:delete": "pm2 delete ecosystem.config.js", "pm2:delete:dev": "pm2 delete ecosystem.dev.config.js", "pm2:status": "pm2 status", "pm2:logs": "pm2 logs", "pm2:monit": "pm2 monit", "logs:setup": "chmod +x setup-logs.sh && ./setup-logs.sh", "logs:view": "./view-logs.sh", "logs:monitor": "./view-logs.sh monitor", "logs:chat": "./view-logs.sh chat-web", "logs:orders": "./view-logs.sh orders", "logs:errors": "./view-logs.sh errors", "logs:size": "./view-logs.sh size"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"dotenv": "^17.2.1", "express": "^5.1.0", "express-session": "^1.18.2", "form-data": "^4.0.4", "node-fetch": "^3.3.2", "playwright": "^1.55.0", "pm2": "^6.0.8", "pusher-js": "^8.4.0", "session-file-store": "^1.5.0", "socket.io": "^4.8.1", "ws": "^8.18.3"}}