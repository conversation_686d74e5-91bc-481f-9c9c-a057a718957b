const fs = require('fs');
const path = require('path');
require('dotenv').config();

const { getChatMessages } = require('./chat-viewer');

// Хранилище для автообновлений (только 1 раз через минуту)
const autoUpdateScheduled = new Set();

const BASE = process.env.BB_BASE_URL || 'https://blazingboost.com';
const SERVICE_LIMIT = Number(process.env.ORDERS_LIMIT || 10);
const POLL_MS = Number(process.env.ORDERS_POLL_MS || 10000);
const STORAGE_STATE = path.join(__dirname, 'storage-state.json');
const LOG_DIR = process.env.ORDERS_LOG_DIR || path.join(__dirname, 'logs');
const LOG_FILE = process.env.ORDERS_LOG_FILE || path.join(LOG_DIR, 'orders.ndjson');
const DETAIL_LOG_FILE = process.env.ORDERS_DETAIL_LOG_FILE || path.join(LOG_DIR, 'orders-details.ndjson');
const READABLE_LOG_FILE = process.env.ORDERS_READABLE_LOG_FILE || path.join(LOG_DIR, 'orders-readable.log');
const TG_TOKEN = process.env.tg_token;
const TG_GROUP_ID = process.env.group_id;
const TG_POLL = (process.env.TG_POLL || '1') === '1';
const MSG_INDEX_FILE = process.env.ORDERS_TG_INDEX || path.join(LOG_DIR, 'tg-message-index.json');
const CHARACTER_LINKS_FILE = path.join(LOG_DIR, 'character-links.ndjson');
const COOKIE_HEADER = process.env.BB_COOKIE_HEADER;

if (!COOKIE_HEADER) {
  console.error('Нужно задать BB_COOKIE_HEADER в .env (строка Cookie)');
  process.exit(1);
}

try { fs.mkdirSync(LOG_DIR, { recursive: true }); } catch {}
function readJsonSafe(file, fallback) {
  try { return JSON.parse(fs.readFileSync(file, 'utf8')); } catch { return fallback; }
}
function writeJsonSafe(file, obj) {
  try { fs.writeFileSync(file, JSON.stringify(obj, null, 2)); } catch {}
}
const messageIndex = readJsonSafe(MSG_INDEX_FILE, {});

function appendNdjson(file, obj) {
  try { fs.appendFileSync(file, JSON.stringify(obj) + '\n'); } catch {}
}

function appendText(file, text) {
  try { fs.appendFileSync(file, text + '\n'); } catch {}
}

function getItemsArray(container) {
  if (!container) return [];
  if (Array.isArray(container)) return container;
  if (Array.isArray(container.items)) return container.items;
  return [];
}

function getValueByName(items, name) {
  const it = items.find(x => (x.name || x.title) === name);
  return it ? (it.value || it.body) : undefined;
}

function parseDateTime(yyyymmdd_hhmm) {
  // '2025/08/25 16:00' -> { date: '25.08.2025', time: '16:00' }
  if (!yyyymmdd_hhmm) return { date: '', time: '' };
  const [dateStr, time] = yyyymmdd_hhmm.split(/\s+/);
  const [y, m, d] = dateStr.split('/');
  const ddmmyyyy = [d, m, y].join('.');
  return { date: ddmmyyyy, time: time || '' };
}

function extractPayout(details) {
  const p = details?.servicePurchaseDetails?.payoutInfo || details?.payoutInfo || '';
  if (typeof p !== 'string') return '';
  const m = p.match(/(\d+[.,]\d+)/);
  if (!m) return '';
  return m[1].replace('.', ',');
}

function isUnsavedRaidersValue(value, difficulty = '') {
  if (!value) return false;
  const v = String(value).trim();
  // Извлекаем первое число до дефиса/пробела/скобки
  const m = v.match(/(\d+)/);
  if (!m) return false;
  const first = parseInt(m[1], 10);
  if (Number.isNaN(first)) return false;

  // Для Normal сложности порог выше - больше 13
  // Для остальных сложностей - больше 7
  const threshold = difficulty === 'Normal' ? 13 : 7;
  return first > threshold;
}

// Функция для поиска ссылок на персонажа в сообщениях чата
async function findCharacterLinkInChat(chatId) {
  if (!chatId) return null;

  try {
    const messages = await getChatMessages(chatId, 100); // Получаем больше сообщений для поиска
    if (!messages || !messages.items) return null;

    // Ищем ссылки в сообщениях
    for (const msg of messages.items) {
      const item = msg.item || {};
      const text = item.text || item.preview || '';

      // Ищем ссылки на worldofwarcraft, raider.io или warcraftlogs
      const linkMatch = text.match(/(https?:\/\/[^\s]+(?:worldofwarcraft|raider\.io|warcraftlogs)[^\s]*)/i);
      if (linkMatch) {
        console.log(`Найдена ссылка на персонажа в чате ${chatId}: ${linkMatch[1]}`);
        return linkMatch[1];
      }
    }

    return null;
  } catch (error) {
    console.warn(`Ошибка поиска ссылки в чате ${chatId}:`, error.message);
    return null;
  }
}

// Функция для поиска заказа по chatId
function findOrderByChatId(chatId) {
  for (const [orderId, msgInfo] of Object.entries(messageIndex)) {
    if (msgInfo.order_chat_id === chatId) {
      return orderId;
    }
  }
  return null;
}

// Функция для обработки найденных ссылок на персонажей из monitor.js
async function processCharacterLinks() {
  if (!fs.existsSync(CHARACTER_LINKS_FILE)) return;

  try {
    const content = fs.readFileSync(CHARACTER_LINKS_FILE, 'utf8');
    const lines = content.trim().split('\n').filter(Boolean);

    // Обрабатываем только новые записи
    const processedLinks = new Set();

    for (const line of lines) {
      try {
        const linkData = JSON.parse(line);
        const { chatId, characterLink, ts } = linkData;

        // Создаем уникальный ключ для избежания дублирования
        const linkKey = `${chatId}:${characterLink}`;
        if (processedLinks.has(linkKey)) continue;
        processedLinks.add(linkKey);

        // Ищем заказ по chatId
        const orderId = findOrderByChatId(chatId);
        if (!orderId) {
          console.log(`Заказ не найден для чата ${chatId}`);
          continue;
        }

        console.log(`🔗 Обрабатываем ссылку на персонажа для заказа ${orderId}: ${characterLink}`);

        // Получаем детали заказа
        const details = await fetchOrderDetails(orderId);

        // Проверяем, есть ли уже ссылка в деталях
        const postItems = getItemsArray(details?.postPurchaseInfo)
          .concat(getItemsArray(details?.postPurchaseInfo?.items));
        const existingProfile = getValueByName(postItems, 'Character Profile') || '';

        if (!existingProfile || !existingProfile.trim()) {
          // Добавляем найденную ссылку в детали
          if (!details.postPurchaseInfo) details.postPurchaseInfo = { items: [] };
          if (!Array.isArray(details.postPurchaseInfo.items)) details.postPurchaseInfo.items = [];
          details.postPurchaseInfo.items.push({
            name: 'Character Profile',
            value: characterLink,
            title: 'Character Profile'
          });

          // Обновляем сообщение в Telegram
          await updateTelegramMessage(orderId, details, chatId);
          console.log(`✅ Обновлен заказ ${orderId} с ссылкой на персонажа из реального времени`);
        } else {
          console.log(`ℹ️ Заказ ${orderId} уже имеет ссылку на персонажа: ${existingProfile}`);
        }

      } catch (error) {
        console.warn('Ошибка обработки ссылки на персонажа:', error.message);
      }
    }

    // Очищаем файл после обработки
    fs.writeFileSync(CHARACTER_LINKS_FILE, '');

  } catch (error) {
    console.warn('Ошибка чтения файла ссылок на персонажей:', error.message);
  }
}

// Функция для автообновления заказа через минуту
async function scheduleAutoUpdate(orderId, chatId) {
  if (autoUpdateScheduled.has(orderId)) {
    return; // Уже запланировано
  }

  autoUpdateScheduled.add(orderId);
  console.log(`Запланировано автообновление заказа ${orderId} через 1 минуту`);

  setTimeout(async () => {
    try {
      console.log(`Выполняется автообновление заказа ${orderId}`);
      const details = await fetchOrderDetails(orderId);

      // Проверяем, появилась ли ссылка в деталях заказа
      const postItems = getItemsArray(details?.postPurchaseInfo)
        .concat(getItemsArray(details?.postPurchaseInfo?.items));
      let characterProfile = getValueByName(postItems, 'Character Profile') || '';

      if (characterProfile && characterProfile.trim()) {
        console.log(`Автообновление: найдена ссылка в деталях заказа ${orderId}: ${characterProfile}`);
        await updateTelegramMessage(orderId, details, chatId);
      } else {
        // Ищем в чате
        const linkFromChat = await findCharacterLinkInChat(chatId);
        if (linkFromChat) {
          console.log(`Автообновление: найдена ссылка в чате заказа ${orderId}: ${linkFromChat}`);
          // Обновляем детали с найденной ссылкой
          if (!details.postPurchaseInfo) details.postPurchaseInfo = { items: [] };
          if (!Array.isArray(details.postPurchaseInfo.items)) details.postPurchaseInfo.items = [];
          details.postPurchaseInfo.items.push({
            name: 'Character Profile',
            value: linkFromChat,
            title: 'Character Profile'
          });
          await updateTelegramMessage(orderId, details, chatId);
        } else {
          console.log(`Автообновление: ссылка на персонажа для заказа ${orderId} не найдена`);
        }
      }
    } catch (error) {
      console.warn(`Ошибка автообновления заказа ${orderId}:`, error.message);
    } finally {
      autoUpdateScheduled.delete(orderId);
    }
  }, 60000); // 1 минута
}

// Функция для обновления сообщения в Telegram
async function updateTelegramMessage(orderId, details, chatId) {
  const msgInfo = messageIndex[orderId];
  if (msgInfo && msgInfo.chat_id && msgInfo.message_id) {
    // Обновляем сохраненный order_chat_id, если он передан
    if (chatId) {
      msgInfo.order_chat_id = chatId;
      messageIndex[orderId] = msgInfo;
      writeJsonSafe(MSG_INDEX_FILE, messageIndex);
    }

    const pretty = formatReadable(details, chatId);
    const replyMarkup = { inline_keyboard: [[{ text: 'Обновить', callback_data: `refresh:${orderId}` }]] };
    const ok = await editTelegram(msgInfo.chat_id, msgInfo.message_id, pretty, replyMarkup);
    console.log('Сообщение в Telegram обновлено:', ok, 'заказ', orderId);
  }
}

function formatReadable(details, chatId = null) {
  const serviceUid = details?.serviceUid || details?.service?.serviceUid || '';
  const serviceName = details?.service?.name || '';

  const attributesItems = getItemsArray(details?.attributes);
  const extraOptionsItems = getItemsArray(details?.extraOptions);
  const postItems = getItemsArray(details?.postPurchaseInfo) // object with items or array
    .concat(getItemsArray(details?.postPurchaseInfo?.items));

  const difficulty = getValueByName(attributesItems, 'Difficulty') || getValueByName(extraOptionsItems, 'Difficulty') || '';
  const runType = getValueByName(attributesItems, 'Run Type') || getValueByName(extraOptionsItems, 'Run Type') || '';
  const lootType = getValueByName(attributesItems, 'Loot Type') || getValueByName(extraOptionsItems, 'Loot Type') || '';
  const premiumLootRunType = getValueByName(attributesItems, 'Premium Loot Run Type') || getValueByName(extraOptionsItems, 'Premium Loot Run Type') || '';
  const unsavedRaiders = getValueByName(attributesItems, 'Unsaved Raiders') || getValueByName(extraOptionsItems, 'Unsaved Raiders') || '';
  const lootDistribution = getValueByName(attributesItems, 'Loot Distribution') || getValueByName(extraOptionsItems, 'Loot Distribution') || '';
  const completionMethod = getValueByName(attributesItems, 'Completion Method') || getValueByName(extraOptionsItems, 'Completion Method') ||
                        getValueByName(attributesItems, 'Completion Type') || getValueByName(extraOptionsItems, 'Completion Type') ||
                        getValueByName(attributesItems, 'Mode') || getValueByName(extraOptionsItems, 'Mode') || '';
  const amountOfBosses = getValueByName(attributesItems, 'Amount of Bosses') || getValueByName(extraOptionsItems, 'Amount of Bosses') || '';
  const dateTimeRaw = getValueByName(attributesItems, 'Date & Time') || getValueByName(extraOptionsItems, 'Date & Time') || '';
  const { date, time } = parseDateTime(dateTimeRaw);
  let characterProfile = getValueByName(postItems, 'Character Profile') || '';
  if (typeof characterProfile === 'string') characterProfile = characterProfile.replace(/\\\//g, '/');
  const price = extractPayout(details);

  const needUnsaved = isUnsavedRaidersValue(unsavedRaiders, difficulty);

  let task;
  if (serviceName === 'Raid Calendar Run') {
    // Не показываем "Loot Sharing Runs" в названии задачи
    const shouldShowLootType = lootType && lootType !== 'Loot Sharing Runs';
    const base = `${difficulty || ''}${difficulty ? ' ' : ''}${runType || ''}${shouldShowLootType ? ` (${lootType})` : ''}`.trim();
    task = `${needUnsaved ? 'Unsaved ' : ''}${base}`.trim();
  } else {
    task = `${needUnsaved ? 'Unsaved ' : ''}${serviceName}`.trim();
  }

  const lines = [
    `1. orderid: ${serviceUid}`,
    `2. task: ${task}`
  ];

  // Добавляем специальное описание для Blazing Battle Package
  if (serviceName === 'Blazing Battle Package') {
    lines.push(`   -Manaforge Heroic + Manaforge Heroic + Manaforge Normal`);
  }

  // Добавляем детали Premium Loot Run Type и Unsaved Raiders если есть
  if (premiumLootRunType) {
    lines.push(`   -Premium Loot Run Type - ${premiumLootRunType}`);
    // Показываем Unsaved Raiders только если есть Premium Loot Run Type
    if (unsavedRaiders) {
      lines.push(`   -Unsaved Raiders ${unsavedRaiders}`);
    }
  }

  // Обрабатываем случай когда Loot Type = "Premium Loot Run"
  if (lootType === 'Premium Loot Run') {
    if (unsavedRaiders) {
      lines.push(`   -Unsaved Raiders ${unsavedRaiders}`);
    }
    if (lootDistribution) {
      // Заменяем "Full Monopoly" на "Full Priority"
      const displayDistribution = lootDistribution === 'Full Monopoly' ? 'Full Priority' : lootDistribution;
      lines.push(`   -${displayDistribution}`);
    }
  }

  // Добавляем Amount of Bosses если есть
  if (amountOfBosses) {
    lines.push(`   -${amountOfBosses}`);
  }

  // Ищем поле с Manaforge в extraOptions
  const manaforgeOption = extraOptionsItems.find(item =>
    item.name && item.name.includes('Manaforge') && item.value === true
  );
  if (manaforgeOption) {
    lines.push(`   -${manaforgeOption.name}`);
  }

  lines.push(
    `3. date:  ${date}`,
    `   time:  ${time}`,
    `4. character: ${characterProfile}`,
    `5. price: ${price}`
  );

  // Добавляем Completion Method всегда (по умолчанию сообщение об уточнении)
  const displayCompletionMethod = completionMethod || 'Селфплей или пилот не найдено информации, уточните';
  lines.push(`6. ${displayCompletionMethod}`);

  lines.push('');

  // Добавляем ссылку на чат в веб-интерфейсе, если есть chatId
  if (chatId) {
    const webPort = process.env.CHAT_WEB_PORT || 61692;
    const chatWebUrl = `http://138.201.175.112:${webPort}/?chat=${chatId}`;
    lines.push(`🔗 Чат: ${chatWebUrl}`);
    lines.push('');
  }

  return lines.join('\n');
}

async function sendTelegram(text, maxRetries = 3) {
  if (!TG_TOKEN || !TG_GROUP_ID) return null;

  const url = `https://api.telegram.org/bot${TG_TOKEN}/sendMessage`;
  const body = {
    chat_id: TG_GROUP_ID,
    text,
    reply_markup: {
      inline_keyboard: [[{ text: 'Обновить', callback_data: 'noop' }]],
    },
    disable_web_page_preview: true,
  };

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Отправка сообщения в Telegram (попытка ${attempt}/${maxRetries})`);

      const res = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });

      if (!res.ok) {
        const t = await res.text();
        console.warn(`TG error (попытка ${attempt}/${maxRetries}):`, res.status, t);

        // Если это последняя попытка, возвращаем null
        if (attempt === maxRetries) {
          console.error(`Не удалось отправить сообщение в Telegram после ${maxRetries} попыток`);
          return null;
        }

        // Ждем перед повторной попыткой (экспоненциальная задержка)
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // 1s, 2s, 4s, max 10s
        console.log(`Ожидание ${delay}ms перед повторной попыткой...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      const data = await res.json();
      console.log(`✅ Сообщение успешно отправлено в Telegram (попытка ${attempt})`);
      return data?.result || null;

    } catch (e) {
      console.warn(`TG send failed (попытка ${attempt}/${maxRetries}):`, e.message);

      // Если это последняя попытка, возвращаем null
      if (attempt === maxRetries) {
        console.error(`Не удалось отправить сообщение в Telegram после ${maxRetries} попыток:`, e.message);
        return null;
      }

      // Ждем перед повторной попыткой
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
      console.log(`Ожидание ${delay}ms перед повторной попыткой...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return null;
}

async function editTelegram(chatId, messageId, text, replyMarkup, maxRetries = 3) {
  if (!TG_TOKEN) return false;

  const url = `https://api.telegram.org/bot${TG_TOKEN}/editMessageText`;
  const body = {
    chat_id: chatId,
    message_id: messageId,
    text,
    disable_web_page_preview: true,
  };
  if (replyMarkup) body.reply_markup = replyMarkup;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const res = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });

      if (!res.ok) {
        const t = await res.text();
        console.warn(`TG edit error (попытка ${attempt}/${maxRetries}):`, res.status, t);

        if (attempt === maxRetries) {
          console.error(`Не удалось обновить сообщение в Telegram после ${maxRetries} попыток`);
          return false;
        }

        // Ждем перед повторной попыткой
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      return true;

    } catch (e) {
      console.warn(`TG edit failed (попытка ${attempt}/${maxRetries}):`, e.message);

      if (attempt === maxRetries) {
        console.error(`Не удалось обновить сообщение в Telegram после ${maxRetries} попыток:`, e.message);
        return false;
      }

      // Ждем перед повторной попыткой
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return false;
}

async function answerCallbackQuery(id, text) {
  if (!TG_TOKEN) return;
  const url = `https://api.telegram.org/bot${TG_TOKEN}/answerCallbackQuery`;
  try {
    await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ callback_query_id: id, text }),
    });
  } catch {}
}

async function pollTelegramUpdates() {
  if (!TG_POLL || !TG_TOKEN) return;
  let offset = 0;
  console.log('Запущен TG long-poll');
  for (;;) {
    try {
      const url = `https://api.telegram.org/bot${TG_TOKEN}/getUpdates?timeout=30&offset=${offset}`;
      const res = await fetch(url);
      const data = await res.json();
      const updates = Array.isArray(data?.result) ? data.result : [];
      for (const up of updates) {
        offset = up.update_id + 1;
        const cq = up.callback_query;
        if (cq && cq.data) {
          console.log('TG callback:', cq.data, 'from', cq.from?.id);
          const msg = cq.message;
          const chatId = msg?.chat?.id;
          const messageId = msg?.message_id;
          // ожидаем data вида 'refresh:<id>'
          if (cq.data.startsWith('refresh:')) {
            const id = Number(cq.data.split(':')[1]);
            await answerCallbackQuery(cq.id, 'Обновляю...');
            try {
              const details = await fetchOrderDetails(id);
              let orderChatId = details?.chatId || details?.chat_id;

              // Если chatId не найден в деталях, попробуем найти его в сохраненных данных
              if (!orderChatId) {
                const msgInfo = messageIndex[id];
                if (msgInfo && msgInfo.order_chat_id) {
                  orderChatId = msgInfo.order_chat_id;
                  console.log(`ChatId восстановлен из индекса для заказа ${id}: ${orderChatId}`);
                } else {
                  console.log(`ChatId не найден для заказа ${id} ни в деталях, ни в индексе`);
                }
              }

              // Проверяем, есть ли ссылка на персонажа
              const postItems = getItemsArray(details?.postPurchaseInfo)
                .concat(getItemsArray(details?.postPurchaseInfo?.items));
              let characterProfile = getValueByName(postItems, 'Character Profile') || '';

              // Если ссылки нет, ищем в чате (только если есть orderChatId)
              if (!characterProfile || !characterProfile.trim()) {
                if (orderChatId) {
                  const linkFromChat = await findCharacterLinkInChat(orderChatId);
                  if (linkFromChat) {
                    console.log(`Найдена ссылка в чате при обновлении заказа ${id}: ${linkFromChat}`);
                    // Добавляем найденную ссылку в детали
                    if (!details.postPurchaseInfo) details.postPurchaseInfo = { items: [] };
                    if (!Array.isArray(details.postPurchaseInfo.items)) details.postPurchaseInfo.items = [];
                    details.postPurchaseInfo.items.push({
                      name: 'Character Profile',
                      value: linkFromChat,
                      title: 'Character Profile'
                    });
                  }
                } else {
                  console.log(`Не удается найти ссылку в чате для заказа ${id} - chatId отсутствует`);
                }
              }

              const pretty = formatReadable(details, orderChatId);
              const replyMarkup = { inline_keyboard: [[{ text: 'Обновить', callback_data: `refresh:${id}` }]] };
              const ok = await editTelegram(chatId, messageId, pretty, replyMarkup);
              console.log('TG edit done:', ok, 'order', id);
            } catch (e) {
              await answerCallbackQuery(cq.id, 'Ошибка обновления');
              console.warn('TG refresh error:', e.message);
            }
          } else {
            await answerCallbackQuery(cq.id, 'Ок');
            console.log('TG callback (other):', cq.data);
          }
        }
      }
    } catch (e) {
      console.warn('TG poll error:', e.message);
      await new Promise(r => setTimeout(r, 3000));
    }
  }
}

async function fetchJson(url, headers) {
  const res = await fetch(url, { headers });
  const text = await res.text();
  if (!res.ok) throw new Error(`HTTP ${res.status} ${res.statusText}: ${text}`);
  try { return JSON.parse(text); } catch { return text; }
}

async function fetchOrderDetails(id) {
  const url = `${BASE}/v1/pro/services/${id}/details`;
  const headers = {
    accept: '*/*',
    cookie: COOKIE_HEADER,
  };
  return fetchJson(url, headers);
}

async function poll() {
  const url = `${BASE}/v1/pro/services?limit=${SERVICE_LIMIT}&offset=0`;
  const headers = {
    accept: '*/*',
    cookie: COOKIE_HEADER,
  };
  const data = await fetchJson(url, headers);
  return data;
}

const seen = new Set();
let isFirstRun = true;

async function main() {
  console.log('Старт мониторинга заказов');
  if (TG_POLL && TG_TOKEN) {
    // запускаем long-poll без ожидания
    pollTelegramUpdates();
  }
  while (true) {
    try {
      // Обрабатываем найденные ссылки на персонажей из monitor.js
      await processCharacterLinks();

      const data = await poll();
      const items = Array.isArray(data?.items) ? data.items : [];
      for (const item of items) {
        const key = `${item.id}:${item.assignedAt}`;
        if (!seen.has(key)) {
          seen.add(key);
          appendNdjson(LOG_FILE, { ts: new Date().toISOString(), event: 'order_seen', item });

          if (isFirstRun) {
            console.log('Первый запуск: пропускаем отправку сообщения для заказа:', item.id, item.assignedAt);
            continue;
          }

          console.log('Новый/необработанный заказ:', item.id, item.assignedAt);
          // подтягиваем детали сразу
          try {
            const details = await fetchOrderDetails(item.id);
            appendNdjson(DETAIL_LOG_FILE, { ts: new Date().toISOString(), event: 'order_detail', id: item.id, details });
            const orderChatId = details?.chatId || details?.chat_id || item.chatId || '';
            console.log('Детали заказа получены:', item.id, orderChatId);

            // Проверяем, есть ли ссылка на персонажа в деталях
            const postItems = getItemsArray(details?.postPurchaseInfo)
              .concat(getItemsArray(details?.postPurchaseInfo?.items));
            let characterProfile = getValueByName(postItems, 'Character Profile') || '';

            // Если ссылки нет, ищем в чате
            if (!characterProfile || !characterProfile.trim()) {
              console.log(`Ссылка на персонажа не найдена в деталях заказа ${item.id}, ищем в чате...`);
              const linkFromChat = await findCharacterLinkInChat(orderChatId);

              if (linkFromChat) {
                console.log(`Найдена ссылка в чате заказа ${item.id}: ${linkFromChat}`);
                // Добавляем найденную ссылку в детали
                if (!details.postPurchaseInfo) details.postPurchaseInfo = { items: [] };
                if (!Array.isArray(details.postPurchaseInfo.items)) details.postPurchaseInfo.items = [];
                details.postPurchaseInfo.items.push({
                  name: 'Character Profile',
                  value: linkFromChat,
                  title: 'Character Profile'
                });
              } else {
                console.log(`Ссылка на персонажа не найдена в чате заказа ${item.id}, планируем автообновление...`);
                // Планируем автообновление через минуту
                scheduleAutoUpdate(item.id, orderChatId);
              }
            }

            const pretty = formatReadable(details, orderChatId);
            appendText(READABLE_LOG_FILE, pretty);
            // отправляем с кнопкой, сохраняем связи для refresh
            const sent = await sendTelegram(pretty);
            if (sent?.chat?.id && sent?.message_id) {
              console.log(`✅ Сообщение для заказа ${item.id} успешно отправлено в Telegram`);
              // обновим кнопку с конкретным orderId
              try {
                const kbUrl = `https://api.telegram.org/bot${TG_TOKEN}/editMessageReplyMarkup`;
                const kbBody = {
                  chat_id: sent.chat.id,
                  message_id: sent.message_id,
                  reply_markup: { inline_keyboard: [[{ text: 'Обновить', callback_data: `refresh:${item.id}` }]] },
                };
                await fetch(kbUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(kbBody) });
              } catch {}
              messageIndex[item.id] = {
                chat_id: sent.chat.id,
                message_id: sent.message_id,
                order_chat_id: orderChatId
              };
              writeJsonSafe(MSG_INDEX_FILE, messageIndex);
            } else {
              console.error(`❌ Не удалось отправить сообщение для заказа ${item.id} в Telegram`);
              // Логируем неудачную отправку
              appendNdjson(DETAIL_LOG_FILE, {
                ts: new Date().toISOString(),
                event: 'telegram_send_failed',
                id: item.id,
                error: 'Failed to send message to Telegram after retries'
              });
            }
          } catch (e) {
            appendNdjson(DETAIL_LOG_FILE, { ts: new Date().toISOString(), event: 'order_detail_error', id: item.id, error: String(e) });
            console.warn('Ошибка деталей заказа', item.id, e.message);
          }
        }
      }
    } catch (e) {
      appendNdjson(LOG_FILE, { ts: new Date().toISOString(), event: 'error', error: String(e) });
      console.warn('Ошибка поллинга:', e.message);
    }

    // После первого цикла сбрасываем флаг
    if (isFirstRun) {
      isFirstRun = false;
      console.log('Первый запуск завершен, теперь будут обрабатываться новые заказы');
    }

    await new Promise(r => setTimeout(r, POLL_MS));
  }
}

main();


