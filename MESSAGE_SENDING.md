# 💬 Отправка сообщений в веб-интерфейсе

## ✅ Что добавлено:

### 1. **API endpoint для отправки сообщений**
- `POST /api/send-message` - защищенный endpoint
- Поддерживает отправку в любой чат
- Использует ваш Bearer токен из `.env`

### 2. **Веб-интерфейс для отправки**
- Поле ввода сообщений внизу экрана
- Автоматическое изменение высоты textarea
- Кнопка отправки с индикацией загрузки
- Горячие клавиши: `Ctrl+Enter` для отправки

### 3. **Интеграция с чатами**
- Поле ввода появляется при выборе чата
- Автоматический фокус на поле ввода
- Очистка поля после отправки

## 🎯 **Как использовать:**

### 1. **Выберите чат:**
- Кликните на любой чат в списке
- Внизу появится поле ввода сообщений

### 2. **Отправьте сообщение:**
- Введите текст в поле ввода
- Нажмите кнопку отправки или `Ctrl+Enter`
- Сообщение отправится от вашего имени

### 3. **Возможности:**
- ✅ Многострочные сообщения
- ✅ Автоматическое изменение высоты поля
- ✅ Индикация отправки
- ✅ Обработка ошибок

## 🔧 **Технические детали:**

### **API запросы:**
```javascript
// 1. Greetings (только если есть channelId)
POST /v2/widget/channels/greetings/send
{
  "id": "channelId"
}

// 2. Отправка сообщения (либо channelId, либо chatId)
POST /v2/widget/messages/send
FormData:
- channelId: string (если есть)
- chatId: string (если нет channelId)
- text: string
- mentions: string (пустая)
```

### **Логика выбора ID:**
1. **Если есть channelId** → используем channelId + greetings
2. **Если нет channelId** → используем chatId (без greetings)
3. **API требует** либо channelId, либо chatId (не оба!)

### **Настройка:**
Убедитесь, что в `.env` настроен:
```bash
CHAT_BEARER=ваш_bearer_токен_здесь
```

### **Безопасность:**
- ✅ Endpoint защищен аутентификацией
- ✅ Использует ваш Bearer токен
- ✅ Валидация входных данных
- ✅ Обработка ошибок

## 🎨 **Интерфейс:**

### **Поле ввода:**
- Появляется только при выборе чата
- Фиксированное положение внизу экрана
- Адаптивная высота (1-5 строк)
- Счетчик символов (макс. 1000)

### **Кнопка отправки:**
- Неактивна для пустых сообщений
- Показывает спиннер при отправке
- Возвращается в исходное состояние после отправки

### **Горячие клавиши:**
- `Ctrl+Enter` - отправить сообщение
- `Enter` - новая строка (обычное поведение)

## ⚠️ **Ограничения:**

1. **Максимум 1000 символов** в сообщении
2. **Только текстовые сообщения** (без файлов)
3. **Требуется активная сессия** (авторизация)
4. **Один чат за раз** (нужно выбрать чат)

## 🐛 **Обработка ошибок:**

### **Возможные ошибки:**
- Нет Bearer токена в настройках
- Недоступен API чатов
- Неверный chatId
- Превышен лимит символов
- Проблемы с сетью

### **Индикация ошибок:**
- Alert с описанием ошибки
- Логирование в консоль браузера
- Восстановление состояния кнопки

## 📱 **Мобильная версия:**

Интерфейс адаптирован для мобильных устройств:
- Поле ввода корректно позиционируется
- Кнопки достаточного размера для касания
- Автофокус работает на мобильных
