# 🔍 Фильтрация чатов

## ✅ Что было настроено:

### 1. **Мониторинг только чатов заказов**
- Система теперь обрабатывает только `chatType: "purchase"`
- Игнорирует `chatType: "seller-support"` (чаты поддержки)
- Игнорирует все остальные типы чатов

### 2. **Где применена фильтрация:**

#### **monitor.js:**
- ✅ `message.new` события - только заказы
- ✅ Все `message.*` события - только заказы  
- ✅ `pollTopChatsOnce()` - только заказы
- ✅ API запросы - только `"types":["purchase"]`

#### **chat-viewer.js:**
- ✅ `getChatsFromAPI()` - только `"types":["purchase"]`

### 3. **Логирование:**
- Добавлено логирование игнорируемых сообщений
- В статистике показывается информация о фильтрации
- В логи добавлено поле `chatType` для отладки

## 🎯 **Результат:**

### ✅ **Будут обрабатываться:**
- Сообщения из чатов заказов (`chatType: "purchase"`)
- Уведомления о новых сообщениях от клиентов
- Автопоиск ссылок на персонажей в заказах

### ❌ **Будут игнорироваться:**
- Сообщения из чатов поддержки (`chatType: "seller-support"`)
- Любые другие типы чатов
- Системные сообщения поддержки

## 📋 **Примеры логов:**

### Игнорируемые сообщения:
```
Игнорируем сообщение из чата поддержки: abc123
Игнорируем сообщение из чата типа "manual-group": def456
Пропускаем чат типа "seller-support" в опросе: ghi789
```

### Обрабатываемые сообщения:
```
Обработано сообщение из чата заказа xyz123: Привет! Когда начинаем?
```

## 🔧 **Настройка:**

Фильтрация настроена автоматически и не требует дополнительной конфигурации.

Если нужно изменить типы чатов:
1. В `monitor.js` измените условие: `if (chatType !== 'purchase')`
2. В API запросах измените: `"types":["purchase"]`

## ⚠️ **Важно:**

- Фильтрация работает на уровне обработки сообщений
- API запросы уже фильтруют чаты по типу
- Логи содержат информацию об игнорируемых сообщениях для отладки
- Веб-интерфейс показывает только отфильтрованные чаты
