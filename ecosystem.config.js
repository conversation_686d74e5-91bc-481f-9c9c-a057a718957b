// Определяем пути логов в зависимости от окружения
const isProduction = process.env.NODE_ENV === 'production';
const logDir = isProduction ? '/home/<USER>' : './logs';

module.exports = {
  apps: [
    {
      name: 'monitor',
      script: 'monitor.js',
      cwd: __dirname,
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        CHAT_LOG_DIR: `${logDir}`,
        ORDERS_LOG_DIR: `${logDir}`
      },
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true
    },
    {
      name: 'chat-web',
      script: 'chat-web.js',
      cwd: __dirname,
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        CHAT_LOG_DIR: `${logDir}`,
        ORDERS_LOG_DIR: `${logDir}`
      },
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true
    },
    {
      name: 'orders-monitor',
      script: 'orders-monitor.js',
      cwd: __dirname,
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        CHAT_LOG_DIR: `${logDir}`,
        ORDERS_LOG_DIR: `${logDir}`
      },
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true
    }
  ]
};
