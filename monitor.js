const Pusher = require('pusher-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Переменные окружения — подставьте из DevTools
const PUSHER_KEY = process.env.CHAT_PUSHER_KEY; // ключ приложения (из инициализации виджета)
const PUSHER_CLUSTER = process.env.CHAT_PUSHER_CLUSTER || 'mt1'; // дефолт, чтобы удовлетворить pusher-js
const PUSHER_HOST = process.env.CHAT_PUSHER_HOST || 'ws.chat-service-blazing-group.com';
const PUSHER_TLS = process.env.CHAT_PUSHER_TLS !== '0';
const PUSHER_PORT = Number(process.env.CHAT_PUSHER_PORT || (PUSHER_TLS ? 2096 : 2095));
const AUTH_ENDPOINT = process.env.CHAT_BROADCAST_AUTH || 'https://api.chat-service-blazing-group.com/v2/auth/broadcasting';
const AUTH_BEARER = process.env.CHAT_BEARER; // тот самый Bearer из запросов
const SOCKET_ID_HINT = process.env.CHAT_SOCKET_ID || undefined; // не обязателен
const CHAT_ID = process.env.CHAT_ID; // например 441b20adc94d7574fd6f49fbd54cfadc
const SELLER_ID = process.env.SELLER_ID; // sellerId для канала widget-<sellerId>
const LOG_DIR = process.env.CHAT_LOG_DIR || path.join(__dirname, 'logs');
const LOG_ALL_FILE = process.env.CHAT_LOG_ALL || path.join(LOG_DIR, `chat-${CHAT_ID}-events.ndjson`);
const LOG_MSG_FILE = process.env.CHAT_LOG_MESSAGES || path.join(LOG_DIR, `chat-${CHAT_ID}-messages.ndjson`);
const CHAT_WEB_PORT = process.env.CHAT_WEB_PORT || 3000;
const POLL_INTERVAL_ACTIVE = Number(process.env.CHAT_POLL_INTERVAL_ACTIVE || 20); // период опроса когда есть активные пользователи
const POLL_INTERVAL_IDLE = Number(process.env.CHAT_POLL_INTERVAL_IDLE || 60); // период опроса когда нет активных пользователей
const POLL_TOP_LIMIT = Number(process.env.CHAT_POLL_TOP_LIMIT || 10); // количество top чатов

let currentPollInterval = POLL_INTERVAL_IDLE; // текущий интервал опроса
let pollTimer = null; // таймер для опроса

if (!PUSHER_KEY || !CHAT_ID || !AUTH_BEARER) {
  console.error('Заполните CHAT_PUSHER_KEY, CHAT_ID и CHAT_BEARER в .env');
  process.exit(1);
}

// ensure log directory exists
try { fs.mkdirSync(LOG_DIR, { recursive: true }); } catch {}

function appendNdjson(file, obj) {
  try { fs.appendFileSync(file, JSON.stringify(obj) + '\n'); } catch {}
}

// Функция для поиска ссылок на персонажа в тексте сообщения
function findCharacterLinkInMessage(text) {
  if (!text) return null;

  // Ищем ссылки на worldofwarcraft, raider.io или warcraftlogs
  const linkMatch = text.match(/(https?:\/\/[^\s]+(?:worldofwarcraft|raider\.io|warcraftlogs)[^\s]*)/i);
  return linkMatch ? linkMatch[1] : null;
}

// Функция для уведомления orders-monitor о найденной ссылке
async function notifyOrdersMonitorAboutCharacterLink(chatId, characterLink) {
  try {
    // Здесь можно добавить HTTP запрос к orders-monitor или использовать файловую систему
    // Пока что просто логируем
    console.log(`🔗 Найдена ссылка на персонажа в чате ${chatId}: ${characterLink}`);

    // Можно добавить запись в специальный файл для orders-monitor
    const LOG_DIR = process.env.ORDERS_LOG_DIR || path.join(__dirname, 'logs');
    const CHARACTER_LINKS_FILE = path.join(LOG_DIR, 'character-links.ndjson');

    appendNdjson(CHARACTER_LINKS_FILE, {
      ts: new Date().toISOString(),
      event: 'character_link_found',
      chatId,
      characterLink,
      source: 'real_time_message'
    });

    console.log(`📝 Ссылка на персонажа записана в ${CHARACTER_LINKS_FILE}`);
  } catch (error) {
    console.warn('Ошибка уведомления orders-monitor о ссылке:', error.message);
  }
}

// Функция для отправки сообщения в веб-интерфейс через HTTP
async function broadcastToWebInterface(chatId, messageData) {
  try {
    const response = await fetch(`http://localhost:${CHAT_WEB_PORT}/api/broadcast-message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chatId,
        message: messageData
      })
    });

    if (response.ok) {
      console.log('Сообщение отправлено в веб-интерфейс для чата:', chatId);
    } else {
      console.warn('Ошибка отправки сообщения в веб-интерфейс:', response.status);
    }
  } catch (error) {
    console.warn('Не удалось отправить сообщение в веб-интерфейс:', error.message);
  }
}

// Pusher в Node
Pusher.Runtime.createSocket = function(url, protocols, options) {
  const WS = require('ws');
  return new WS(url, protocols, options);
};

const pusher = new Pusher(PUSHER_KEY, {
  cluster: PUSHER_CLUSTER,
  wsHost: PUSHER_HOST,
  wsPort: PUSHER_TLS ? PUSHER_PORT : PUSHER_PORT,
  wssPort: PUSHER_PORT,
  forceTLS: PUSHER_TLS,
  // Кастомный authorizer: сервер ждёт JSON и заголовок x-socket-id
  authorizer: (channel, options) => {
    return {
      authorize: async (socketId, callback) => {
        try {
          const res = await fetch(AUTH_ENDPOINT, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${AUTH_BEARER}`,
              'Content-Type': 'application/json',
              'x-socket-id': socketId,
            },
            body: JSON.stringify({ socket_id: socketId, channel_name: channel.name }),
          });
          if (!res.ok) {
            const text = await res.text();
            return callback(new Error(`Auth failed ${res.status}: ${text}`), { auth: '' });
          }
          const data = await res.json();
          // Ожидается объект вида { auth: "...", channel_data?: "..." }
          return callback(null, data);
        } catch (e) {
          return callback(e, { auth: '' });
        }
      },
    };
  },
  enabledTransports: ['ws', 'wss'],
  disableStats: true,
  // Дополнительно: отключить xhr/fallback
  transports: ['ws'],
});

// Пер-чат канал не используем. Оставляем только widget-<SELLER_ID> для сообщений.

// Подписываемся на канал сообщений widget-<sellerId>, если задан SELLER_ID
let widgetChannel = null;
if (SELLER_ID) {
  const widgetChannelName = `widget-${SELLER_ID}`;
  widgetChannel = pusher.subscribe(widgetChannelName);

  widgetChannel.bind('pusher:subscription_succeeded', () => {
    console.log('Подписка оформлена на', widgetChannelName);
  });

  // Основное событие новых сообщений
  widgetChannel.bind('message.new', async (raw) => {
    try {
      const payload = typeof raw === 'string' ? JSON.parse(raw) : raw;
      const m = payload && payload.message ? payload.message : null;
      if (!m) return;

      // Фильтруем только чаты заказов, игнорируем поддержку
      const chatType = m.chatType || '';
      if (chatType === 'seller-support') {
        console.log(`Игнорируем сообщение из чата поддержки: ${m.chatId}`);
        return;
      }

      // Логируем только сообщения из чатов заказов
      if (chatType !== 'purchase') {
        console.log(`Игнорируем сообщение из чата типа "${chatType}": ${m.chatId}`);
        return;
      }

      const webMsg = {
        type: m.type === 'system-message' ? 'system-message' : 'user-message',
        item: {
          timestamp: m.timestamp || Math.floor(Date.now() / 1000),
          author: m.author || { name: 'Система' },
          text: m.text || m.preview || '',
          preview: m.preview || m.text || '',
        },
      };

      // Логируем в файл сообщений только сообщения заказов
      appendNdjson(LOG_MSG_FILE, { ts: new Date().toISOString(), event: 'message.new', data: payload, chatId: m.chatId, chatType: m.chatType });

      // Ретрансляция в веб-интерфейс по chatId только для заказов
      broadcastToWebInterface(m.chatId, webMsg);

      console.log(`Обработано сообщение из чата заказа ${m.chatId}: ${m.text || m.preview || '[без текста]'}`);

      // Проверяем, есть ли ссылка на персонажа в новом сообщении
      const messageText = m.text || m.preview || '';
      const characterLink = findCharacterLinkInMessage(messageText);
      if (characterLink) {
        console.log(`🔗 Найдена ссылка на персонажа в реальном времени: ${characterLink}`);
        await notifyOrdersMonitorAboutCharacterLink(m.chatId, characterLink);
      }

      // Обновляем метку последнего события для top-поллинга
      try {
        const prev = topChatsState.get(m.chatId) || { lastTs: 0, unread: 0, initialized: true };
        const ts = m.timestamp || Math.floor(Date.now() / 1000);
        topChatsState.set(m.chatId, { lastTs: Math.max(prev.lastTs, ts), unread: prev.unread, initialized: true });
      } catch {}
    } catch (e) {
      console.warn('Ошибка обработки message.new:', e.message);
    }
  });

  // На всякий случай: ловим все message.*
  widgetChannel.bind_global((eventName, raw) => {
    if (!eventName || !eventName.startsWith('message.')) return;
    if (eventName === 'message.new') return; // уже обработано выше
    try {
      const payload = typeof raw === 'string' ? JSON.parse(raw) : raw;
      const m = payload && payload.message ? payload.message : null;
      if (!m) return;

      // Фильтруем только чаты заказов
      const chatType = m.chatType || '';
      if (chatType !== 'purchase') {
        return; // Игнорируем все кроме заказов
      }

      appendNdjson(LOG_MSG_FILE, { ts: new Date().toISOString(), event: eventName, data: payload, chatId: m.chatId, chatType: m.chatType });
    } catch {}
  });
}

const CONNECTION_HEALTH_CHECK_INTERVAL = 5 * 60 * 1000; // Проверка здоровья соединения каждые 5 минут

pusher.connection.bind('connected', () => {
  console.log('Pusher connected. Socket ID:', pusher.connection.socket_id);
  appendNdjson(LOG_ALL_FILE, { ts: new Date().toISOString(), event: 'connected', socket_id: pusher.connection.socket_id });
});

pusher.connection.bind('disconnected', () => {
  console.log('Pusher disconnected. Attempting to reconnect...');
  appendNdjson(LOG_ALL_FILE, { ts: new Date().toISOString(), event: 'disconnected' });
});

pusher.connection.bind('error', (error) => {
  console.warn('Pusher connection error:', error);
  appendNdjson(LOG_ALL_FILE, { ts: new Date().toISOString(), event: 'connection_error', error: error.toString() });
});

pusher.connection.bind('failed', () => {
  console.error('Pusher connection failed permanently');
  appendNdjson(LOG_ALL_FILE, { ts: new Date().toISOString(), event: 'connection_failed' });
});

// Обработка общих ошибок Pusher
pusher.bind('pusher:error', (error) => {
  console.warn('Pusher error:', error);
  appendNdjson(LOG_ALL_FILE, { ts: new Date().toISOString(), event: 'pusher_error', error });
});

// Функция для вывода статистики подписок
function logSubscriptionStats() {
  const activeChannels = Object.keys(pusher.channels.channels).length;
  const connectionState = pusher.connection.state;

  console.log(`\n=== Статистика мониторинга ===`);
  console.log(`Время: ${new Date().toLocaleString('ru-RU')}`);
  console.log(`Состояние соединения: ${connectionState}`);
  console.log(`Основной чат: ${CHAT_ID}`);
  console.log(`Фильтрация: только чаты заказов (purchase)`);
  console.log(`Игнорируем: чаты поддержки (seller-support)`);
  console.log(`Дополнительных подписок: 0 (пер-чат подписки отключены)`);
  console.log(`Всего активных каналов: ${activeChannels}`);
  console.log(`Socket ID: ${pusher.connection.socket_id || 'не подключен'}`);
  console.log(`===============================\n`);
}

// Выводим статистику каждый час
setInterval(logSubscriptionStats, 60 * 60 * 1000);

// Выводим первую статистику через 5 минут после запуска
setTimeout(logSubscriptionStats, 5 * 60 * 1000);

// Мониторинг здоровья соединения
function checkConnectionHealth() {
  const connectionState = pusher.connection.state;
  const activeChannels = Object.keys(pusher.channels.channels).length;

  console.log(`\n=== Проверка здоровья соединения ===`);
  console.log(`Состояние: ${connectionState}`);
  console.log(`Активных каналов: ${activeChannels}`);
  console.log(`Подписанных чатов: 0 (пер-чат подписки отключены)`);

  if (connectionState !== 'connected') {
    console.warn('⚠️  Соединение не активно, попытка переподключения...');
    pusher.disconnect();
    setTimeout(() => {
      pusher.connect();
    }, 5000);
  } else {
    console.log('✅ Соединение здоровое');
  }
  console.log(`=====================================\n`);
}

// Запускаем проверку здоровья соединения
setInterval(checkConnectionHealth, CONNECTION_HEALTH_CHECK_INTERVAL);

pusher.connection.bind('error', (err) => {
  console.error('Pusher error:', err);
  appendNdjson(LOG_ALL_FILE, { ts: new Date().toISOString(), event: 'error', err });
});

// Функция для проверки количества активных пользователей в веб-интерфейсе
async function checkActiveUsers() {
  try {
    const response = await fetch(`http://localhost:${CHAT_WEB_PORT}/api/connected-users`);
    if (response.ok) {
      const data = await response.json();
      return data.hasActiveUsers;
    }
  } catch (error) {
    console.warn('Не удалось проверить активных пользователей:', error.message);
  }
  return false; // по умолчанию считаем что пользователей нет
}

// Функция для динамического управления интервалом опроса
async function updatePollInterval() {
  const hasActiveUsers = await checkActiveUsers();
  const newInterval = hasActiveUsers ? POLL_INTERVAL_ACTIVE : POLL_INTERVAL_IDLE;

  if (newInterval !== currentPollInterval) {
    currentPollInterval = newInterval;
    console.log(`🔄 Изменен интервал опроса: ${newInterval}с (активные пользователи: ${hasActiveUsers ? 'да' : 'нет'})`);

    // Перезапускаем таймер с новым интервалом
    if (pollTimer) {
      clearInterval(pollTimer);
    }
    startPolling();
  }
}

// Функция для получения списка чатов из API
async function getChatsFromAPI(limit = 100, page = 0) {
  // Добавляем случайную задержку для обхода Cloudflare (1-3 секунды)
  const delay = Math.random() * 2000 + 1000;
  await new Promise(resolve => setTimeout(resolve, delay));

  const url = `https://api.chat-service-blazing-group.com/v2/widget/chats/list?limit=${limit}&page=${page}&filters=%7B%22like%22:%22%22,%22types%22:[%22purchase%22]%7D`;

  try {
    const response = await fetch(url, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7',
        'authorization': `Bearer ${AUTH_BEARER}`,
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'referer': 'https://blazingboost.com/',
        'origin': 'https://blazingboost.com',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Ошибка API чатов ${response.status}:`, errorText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Ошибка запроса API чатов:', error.message);
    return null;
  }
}

// Точечный запрос сообщений по чату
async function getChatMessagesFromAPI(chatId, limit = 10) {
  const url = `https://api.chat-service-blazing-group.com/v2/widget/messages/chat/${chatId}/previous?limit=${limit}`;
  try {
    const response = await fetch(url, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7',
        'authorization': `Bearer ${AUTH_BEARER}`,
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
      },
    });
    if (!response.ok) return null;
    const data = await response.json();
    return data && Array.isArray(data.items) ? data.items : [];
  } catch {
    return null;
  }
}

// Состояние top чатов: запоминаем последний timestamp и unread
const topChatsState = new Map(); // chatId -> { lastTs: number, unread: number, initialized: boolean }

function toWebMsgFromApiItem(msg) {
  const it = msg.item || {};
  return {
    type: msg.type && msg.type.includes('system') ? 'system-message' : 'user-message',
    item: {
      timestamp: it.timestamp || Math.floor(Date.now() / 1000),
      author: it.author || { name: 'Система' },
      text: it.text || it.preview || '',
      preview: it.preview || it.text || ''
    }
  };
}

async function pollTopChatsOnce() {
  try {
    // Проверяем и обновляем интервал опроса перед каждым запросом
    await updatePollInterval();

    const data = await getChatsFromAPI(POLL_TOP_LIMIT, 0);
    if (!data || !Array.isArray(data.items)) return;

    for (const chat of data.items) {
      if (!chat || !chat.id) continue;

      // Дополнительная фильтрация: только чаты заказов
      const chatType = chat.type || '';
      if (chatType !== 'purchase') {
        console.log(`Пропускаем чат типа "${chatType}" в опросе: ${chat.id}`);
        continue;
      }

      const chatId = chat.id;
      const latestTs = (chat.lastMessage && chat.lastMessage.timestamp) || chat.timestamp || 0;
      const unread = chat.unreadMessagesCount || 0;
      const prev = topChatsState.get(chatId) || { lastTs: 0, unread: 0, initialized: false };

      // При первом появлении чата в поллинге — только инициализируем состояние, без ретрансляции истории
      if (!prev.initialized) {
        topChatsState.set(chatId, { lastTs: latestTs, unread, initialized: true });
        continue;
      }

      const hasNewActivity = latestTs > prev.lastTs || unread > prev.unread;
      if (!hasNewActivity) continue;

      // Запрашиваем только если видим новую активность
      const items = await getChatMessagesFromAPI(chatId, 10);
      if (items && items.length > 0) {
        // Выбираем только новые по сравнению с prev.lastTs
        const newItems = items.filter(x => x.item && x.item.timestamp && x.item.timestamp > prev.lastTs);
        newItems.sort((a,b)=> (a.item.timestamp||0)-(b.item.timestamp||0));
        for (const msg of newItems) {
          const webMsg = toWebMsgFromApiItem(msg);
          broadcastToWebInterface(chatId, webMsg);
        }
      }

      // Обновляем состояние по чату
      topChatsState.set(chatId, { lastTs: latestTs, unread, initialized: true });
    }
  } catch (e) {
    console.warn('Ошибка опроса top чатов:', e.message);
  }
}

// Функция для запуска опроса с текущим интервалом
function startPolling() {
  if (pollTimer) {
    clearInterval(pollTimer);
  }

  if (currentPollInterval > 0) {
    console.log(`🚀 Запуск опроса с интервалом ${currentPollInterval}с`);
    pollTimer = setInterval(pollTopChatsOnce, currentPollInterval * 1000);
  }
}

// Запускаем динамический опрос
startPolling();

// Пер-чат подписки отключены
async function subscribeToChatsWithDelay() { return { successCount: 0, errorCount: 0 }; }

// Функция для подписки на все чаты из API
async function subscribeToAllChatsFromAPI() {
  try {
    console.log('Пер-чат подписки отключены. Используем только widget-<SELLER_ID> для сообщений.');

  } catch (error) {
    console.error('Ошибка при подписке на чаты из API:', error.message);
    // Ничего не делаем: realtime идёт через widget-<SELLER_ID>
  }
}

// Функция для загрузки и подписки на чаты из локального файла (fallback)
function subscribeToKnownChatsFromFile() { /* отключено */ }

process.on('SIGINT', () => {
  console.log('Завершение...');
  pusher.disconnect();
  process.exit(0);
});

// Подписываемся на все чаты из API после подключения к Pusher
setTimeout(async () => {
  await subscribeToAllChatsFromAPI();
}, 2000); // Даем время на подключение к основному каналу

// Периодически обновляем список чатов и проверяем активных пользователей (каждые 30 минут)
setInterval(async () => {
  console.log('Периодическое обновление списка чатов...');
  try {
    // Проверяем активных пользователей и обновляем интервал
    await updatePollInterval();

    // Пер-чат подписки отключены; оставляем пинг для здоровья соединения
    await getChatsFromAPI(1, 0);
  } catch (error) {
    console.warn('Ошибка периодического обновления чатов:', error.message);
  }
}, 30 * 60 * 1000); // 30 минут


