# PowerShell скрипт для запуска всех сервисов через PM2
Write-Host "🚀 Запуск всех сервисов BlazingBoost..." -ForegroundColor Green

# Проверяем, установлен ли PM2
if (!(Get-Command pm2 -ErrorAction SilentlyContinue)) {
    Write-Host "❌ PM2 не установлен. Устанавливаем..." -ForegroundColor Red
    npm install -g pm2
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Ошибка установки PM2" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ PM2 успешно установлен" -ForegroundColor Green
}

# Запускаем все сервисы
Write-Host "📦 Запуск сервисов через PM2..." -ForegroundColor Yellow
pm2 start ecosystem.config.js

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Все сервисы успешно запущены!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📊 Статус сервисов:" -ForegroundColor Cyan
    pm2 status
    Write-Host ""
    Write-Host "🌐 Доступные интерфейсы:" -ForegroundColor Cyan
    Write-Host "   • Chat Web: http://localhost:3000" -ForegroundColor White
    Write-Host ""
    Write-Host "📝 Полезные команды:" -ForegroundColor Cyan
    Write-Host "   • npm run pm2:status  - статус сервисов" -ForegroundColor White
    Write-Host "   • npm run pm2:logs    - просмотр логов" -ForegroundColor White
    Write-Host "   • npm run pm2:stop    - остановить все" -ForegroundColor White
    Write-Host "   • npm run pm2:restart - перезапустить все" -ForegroundColor White
    Write-Host "   • npm run pm2:monit   - мониторинг в реальном времени" -ForegroundColor White
} else {
    Write-Host "❌ Ошибка запуска сервисов" -ForegroundColor Red
    exit 1
}
