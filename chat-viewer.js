const fs = require('fs');
const path = require('path');
require('dotenv').config();

const BASE = process.env.BB_BASE_URL || 'https://blazingboost.com';
const COOKIE_HEADER = process.env.BB_COOKIE_HEADER;
const CHAT_BEARER = process.env.CHAT_BEARER;

if (!COOKIE_HEADER) {
  console.error('Нужно задать BB_COOKIE_HEADER в .env');
  process.exit(1);
}

if (!CHAT_BEARER) {
  console.error('Нужно задать CHAT_BEARER в .env');
  process.exit(1);
}

async function getChatMessages(chatId, limit = 50) {
  const url = `https://api.chat-service-blazing-group.com/v2/widget/messages/chat/${chatId}/previous?limit=${limit}`;
  
  try {
    const response = await fetch(url, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7',
        'authorization': `Bearer ${CHAT_BEARER}`,
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'cookie': COOKIE_HEADER,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      // Логируем только серьезные ошибки, CHT3016 - это обычная ошибка доступа
      if (response.status !== 400 || !errorText.includes('CHT3016')) {
        console.error(`Ошибка ${response.status}:`, errorText);
      }
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Ошибка запроса:', error.message);
    return null;
  }
}

async function viewChat(chatId) {
  console.log(`=== Просмотр чата ${chatId} ===\n`);
  
  const messages = await getChatMessages(chatId);
  if (!messages) {
    console.log('Не удалось получить сообщения');
    return;
  }

  console.log(`Найдено сообщений: ${messages.total || 0}\n`);

  if (messages.items && Array.isArray(messages.items)) {
    messages.items.forEach((msg, index) => {
      const item = msg.item;
      const time = new Date(item.timestamp * 1000).toLocaleString('ru-RU');
      const sender = item.author?.name || 'Система';
      const content = item.text || item.preview || '[без текста]';
      const type = msg.type === 'system-message' ? '[СИСТЕМА]' : 
                   msg.type === 'user-message' ? '[ПОЛЬЗОВАТЕЛЬ]' : '[ДРУГОЕ]';
      
      console.log(`${index + 1}. [${time}] ${type} ${sender}:`);
      console.log(`   ${content}`);
      console.log('');
    });
  } else {
    console.log('Структура ответа:', JSON.stringify(messages, null, 2));
  }
}

async function listChats() {
  const LOG_DIR = process.env.ORDERS_LOG_DIR || path.join(__dirname, 'logs');
  const ORDERS_FILE = path.join(LOG_DIR, 'orders.ndjson');

  if (!fs.existsSync(ORDERS_FILE)) {
    console.log('Файл orders.ndjson не найден. Сначала запустите монитор заказов.');
    return new Map();
  }

  const lines = fs.readFileSync(ORDERS_FILE, 'utf8').split('\n').filter(Boolean);
  const orders = lines.map(line => JSON.parse(line));
  
  const uniqueChats = new Map();
  
  orders.forEach(order => {
    if (order.event === 'order_seen' && order.item.chatId) {
      const chatId = order.item.chatId;
      const orderId = order.item.id;
      const serviceName = order.item.service.name;
      const serviceUid = order.item.serviceUid;
      
      if (!uniqueChats.has(chatId)) {
        uniqueChats.set(chatId, {
          chatId,
          orders: [],
          serviceName,
          serviceUids: []
        });
      }
      
      uniqueChats.get(chatId).orders.push(orderId);
      if (serviceUid && !uniqueChats.get(chatId).serviceUids.includes(serviceUid)) {
        uniqueChats.get(chatId).serviceUids.push(serviceUid);
      }
    }
  });

  return uniqueChats;
}

async function findChatByServiceUid(serviceUid) {
  // Сначала пробуем найти в локальных данных
  const chats = await listChats();
  
  for (const [chatId, info] of chats) {
    if (info.serviceUids.includes(serviceUid)) {
      return { chatId, info };
    }
  }
  
  // Если не найдено локально, ищем через API
  try {
    const url = `https://api.chat-service-blazing-group.com/v2/widget/chats/list?limit=20&page=0&filters=%7B%22like%22:%22${serviceUid}%22%7D`;
    
    const response = await fetch(url, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7',
        'authorization': `Bearer ${CHAT_BEARER}`,
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'cookie': COOKIE_HEADER,
      },
    });

    if (!response.ok) {
      console.error(`Ошибка API поиска ${response.status}:`, await response.text());
      return null;
    }

    const data = await response.json();
    
    if (data.items && data.items.length > 0) {
      const chat = data.items[0]; // Берем первый найденный чат
      return {
        chatId: chat.id,
        info: {
          chatId: chat.id,
          orders: [serviceUid], // Показываем только искомый Service UID
          serviceName: chat.title,
          serviceUids: [serviceUid],
          apiData: chat // Сохраняем полные данные от API
        }
      };
    }
  } catch (error) {
    console.error('Ошибка API поиска:', error.message);
  }
  
  return null;
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    const chats = await listChats();
    console.log('=== Доступные чаты ===\n');
    
    let index = 1;
    for (const [chatId, info] of chats) {
      console.log(`${index}. Chat ID: ${chatId}`);
      console.log(`   Заказы: ${info.orders.join(', ')}`);
      console.log(`   Service UIDs: ${info.serviceUids.join(', ')}`);
      console.log(`   Сервис: ${info.serviceName}`);
      console.log(`   Команда: node chat-viewer.js ${chatId}`);
      console.log('');
      index++;
    }
    
    console.log('Для просмотра чата используйте:');
    console.log('  node chat-viewer.js <chatId>');
    console.log('  node chat-viewer.js --service <serviceUid>');
    return;
  }

  if (args[0] === '--service' && args[1]) {
    const serviceUid = args[1];
    console.log(`=== Поиск чата по Service UID: ${serviceUid} ===\n`);
    
    const result = await findChatByServiceUid(serviceUid);
    if (result) {
      console.log(`Найден чат:`);
      console.log(`  Chat ID: ${result.chatId}`);
      console.log(`  Заказы: ${result.info.orders.join(', ')}`);
      console.log(`  Service UIDs: ${result.info.serviceUids.join(', ')}`);
      console.log(`  Сервис: ${result.info.serviceName}`);
      console.log('');
      await viewChat(result.chatId);
    } else {
      console.log(`Чат с Service UID ${serviceUid} не найден.`);
    }
    return;
  }

  const chatId = args[0];
  await viewChat(chatId);
}

if (require.main === module) {
  main().catch(console.error);
}

// Функция для получения списка чатов из API с информацией о непрочитанных
async function getChatsFromAPI(limit = 50, page = 0) {
  // Добавляем случайную задержку для обхода Cloudflare (1-3 секунды)
  const delay = Math.random() * 2000 + 1000;
  await new Promise(resolve => setTimeout(resolve, delay));

  const url = `https://api.chat-service-blazing-group.com/v2/widget/chats/list?limit=${limit}&page=${page}&filters=%7B%22like%22:%22%22,%22types%22:[%22purchase%22]%7D`;

  try {
    const response = await fetch(url, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7',
        'authorization': `Bearer ${CHAT_BEARER}`,
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'referer': 'https://blazingboost.com/',
        'origin': 'https://blazingboost.com',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'cookie': COOKIE_HEADER,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Ошибка API чатов ${response.status}:`, errorText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Ошибка запроса API чатов:', error.message);
    return null;
  }
}

module.exports = { getChatMessages, viewChat, listChats, findChatByServiceUid, getChatsFromAPI };
