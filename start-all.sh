#!/bin/bash

# Bash скрипт для запуска всех сервисов через PM2
echo "🚀 Запуск всех сервисов BlazingBoost..."

# Проверяем, установлен ли PM2
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 не установлен. Устанавливаем..."
    npm install -g pm2
    if [ $? -ne 0 ]; then
        echo "❌ Ошибка установки PM2"
        exit 1
    fi
    echo "✅ PM2 успешно установлен"
fi

# Запускаем все сервисы
echo "📦 Запуск сервисов через PM2..."
pm2 start ecosystem.config.js

if [ $? -eq 0 ]; then
    echo "✅ Все сервисы успешно запущены!"
    echo ""
    echo "📊 Статус сервисов:"
    pm2 status
    echo ""
    echo "🌐 Доступные интерфейсы:"
    echo "   • Chat Web: http://localhost:3000"
    echo ""
    echo "📝 Полезные команды:"
    echo "   • npm run pm2:status  - статус сервисов"
    echo "   • npm run pm2:logs    - просмотр логов"
    echo "   • npm run pm2:stop    - остановить все"
    echo "   • npm run pm2:restart - перезапустить все"
    echo "   • npm run pm2:monit   - мониторинг в реальном времени"
else
    echo "❌ Ошибка запуска сервисов"
    exit 1
fi
