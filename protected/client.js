/* BlazingBoost Live Chat client */
/* global io, Notification */
const socket = io();
let currentChatId = '';
let lastMessageCount = 0;
let autoScroll = true;
let notificationsEnabled = false; // Браузерные уведомления отключены
let soundNotificationsEnabled = true; // Звуковые уведомления включены по умолчанию

let allChats = [];
let currentChannelId = null; // Для отправки сообщений  

// Наш ID для исключения уведомлений на собственные сообщения (получаем с сервера)
const OUR_SELLER_ID = window.OUR_SELLER_ID || '';

// Грация для подавления начального всплеска уведомлений
const suppressNotificationsUntil = Date.now() + 20_000;

// Пер-чат кэш увиденных сообщений (по ключу) для жесткой дедупликации
const seenMessageKeysByChat = new Map(); // chatId -> Set<string>
function getSeenSet(chatId) {
  let set = seenMessageKeysByChat.get(chatId);
  if (!set) { set = new Set(); seenMessageKeysByChat.set(chatId, set); }
  return set;
}

// Дедуп ключ сообщения: используем id, иначе составляем из timestamp+author+text
function getMessageKeyFromItem(item) {
  const id = item && (item.id || item._id);
  if (id) return String(id);
  const ts = item && item.timestamp ? String(item.timestamp) : '0';
  const author = (item && item.author && item.author.name) ? item.author.name : 'unknown';
  const text = (item && (item.text || item.preview)) ? (item.text || item.preview) : '';
  return `${ts}::${author}::${text}`;
}

// Проверка, является ли сообщение нашим (от нашего аккаунта)
function isOurMessage(messageData) {
  const item = messageData.item || {};
  const author = item.author || {};

  // Проверяем по ID автора или senderId
  const isOurId = author.id === OUR_SELLER_ID ||
                  messageData.senderId === OUR_SELLER_ID ||
                  item.senderId === OUR_SELLER_ID;

  // Дополнительная проверка по типу автора (seller)
  const isSellerType = author.type === 'seller';

  // Логируем для отладки
  if (isOurId || isSellerType) {
    console.log('Обнаружено наше сообщение:', {
      authorId: author.id,
      authorType: author.type,
      senderId: messageData.senderId || item.senderId,
      ourId: OUR_SELLER_ID,
      isOurId,
      isSellerType
    });
  }

  return isOurId;
}

// Вставка сообщения в DOM с упорядочиванием по timestamp и защитой от дублей
function insertMessageInOrder(message) {
  const item = message.item || {};
  const sender = item.author?.name || 'Система';
  const content = item.text || item.preview || '[без текста]';
  const time = new Date((item.timestamp || 0) * 1000).toLocaleString('ru-RU');
  const type = message.type === 'system-message' ? 'system' : 'user';
  const avatar = sender.charAt(0).toUpperCase();
  const key = getMessageKeyFromItem(item);
  const ts = item.timestamp || 0;

  const messagesContainer = document.getElementById('messagesContainer');
  if (!messagesContainer) return false;

  // Жесткая проверка дубля через DOM
  const existing = Array.from(messagesContainer.getElementsByClassName('message'))
    .find(el => el.dataset && el.dataset.messageKey === key);
  if (existing) return false; // дубль — не вставляем

  const messageHtml = `
      <div class="message ${type}" data-message-key="${key}" data-ts="${ts}">
        <div class="message-avatar">${avatar}</div>
        <div class="message-content">
          <div class="message-header">
            <div class="message-sender">${sender}</div>
            <div class="message-time">${time}</div>
          </div>
          <div class="message-text">${content.replace(/\n/g, '<br>')}</div>
        </div>
      </div>
    `;

  // Ищем позицию для вставки (перед первым сообщением с большим timestamp)
  const children = Array.from(messagesContainer.getElementsByClassName('message'));
  let inserted = false;
  for (const child of children) {
    const childTs = Number(child.dataset.ts || '0');
    const childKey = child.dataset.messageKey || '';
    if (ts < childTs || (ts === childTs && key < childKey)) {
      child.insertAdjacentHTML('beforebegin', messageHtml);
      inserted = true;
      break;
    }
  }
  if (!inserted) {
    messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
  }
  return true;
}

// Запрос разрешения на уведомления
async function requestNotificationPermission() {
  // Браузерные уведомления отключены, используем только звуковые
  console.log('🔊 Звуковые уведомления включены по умолчанию');

  // Тестовый звук при загрузке отключен

  updateNotificationStatus();
}

// Функция showTestNotification удалена - используем только звуковые уведомления

function updateNotificationStatus() {
  const indicator = document.querySelector('.online-indicator');
  const btn = document.getElementById('notificationBtn');
  const testBtn = document.getElementById('testNotificationBtn');

  if (indicator) {
    if (soundNotificationsEnabled) {
      indicator.style.background = '#17a2b8'; // Синий цвет для звуковых уведомлений
      indicator.title = 'Звуковые уведомления включены - клик для теста';
    } else {
      indicator.style.background = '#ffc107'; // Желтый цвет когда отключены
      indicator.title = 'Звуковые уведомления отключены - нажмите для включения';
    }
  }

  if (btn) {
    if (soundNotificationsEnabled) {
      btn.innerHTML = '<i class="fas fa-volume-up"></i> Звуковые уведомления включены';
      btn.className = 'notification-btn enabled';
      btn.style.display = 'flex';
    } else {
      btn.innerHTML = '<i class="fas fa-volume-mute"></i> Включить звуковые уведомления';
      btn.className = 'notification-btn';
      btn.style.display = 'flex';
    }
  }

  if (testBtn) {
    testBtn.style.display = soundNotificationsEnabled ? 'flex' : 'none';
  }
}

async function toggleNotifications() {
  // Переключаем только звуковые уведомления
  soundNotificationsEnabled = !soundNotificationsEnabled;

  if (soundNotificationsEnabled) {
    console.log('🔊 Звуковые уведомления включены');
    playNotificationSound(); // Тестовый звук
  } else {
    console.log('� Звуковые уведомления отключены');
  }

  updateNotificationStatus();
}

function testNotification() {
  if (soundNotificationsEnabled) {
    playNotificationSound();
    // Показываем временное сообщение о тесте звука
    const indicator = document.querySelector('.online-indicator');
    if (indicator) {
      const originalTitle = indicator.title;
      indicator.title = '🔊 Тестовый звук воспроизведен!';
      setTimeout(() => {
        indicator.title = originalTitle;
      }, 2000);
    }
    console.log('🧪 Тест звукового уведомления выполнен');
  } else {
    alert('Звуковые уведомления отключены!');
  }
}

function handleIndicatorClick() {
  if (soundNotificationsEnabled) {
    testNotification();
  } else {
    toggleNotifications();
  }
}

function playNotificationSound() {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // Более длинный и приятный звук уведомления
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.2);
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.4);
    oscillator.frequency.setValueAtTime(500, audioContext.currentTime + 0.6);

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.8);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.8);
    console.log('🔊 Звук уведомления воспроизведен');
  } catch (error) {
    console.log('Не удалось воспроизвести звук уведомления:', error.message);
  }
}

function playActiveTabSound() {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // Короткий деликатный звук для активного чата (как в Telegram/WhatsApp)
    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
    oscillator.frequency.setValueAtTime(500, audioContext.currentTime + 0.05);

    gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.15);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.15);
    console.log('🔔 Короткий звук для активного чата воспроизведен');
  } catch (error) {
    console.log('Не удалось воспроизвести звук для активного чата:', error.message);
  }
}

function showNotification(title, body, chatId, messageData = null) {
  // Подавляем в первые секунды после запуска, чтобы избежать всплеска из первичного poll
  if (Date.now() < suppressNotificationsUntil) return;
  if (!soundNotificationsEnabled) return;

  // Дополнительная проверка на наше сообщение, если передан messageData
  if (messageData && isOurMessage(messageData)) {
    console.log('Блокировка уведомления: это наше сообщение');
    return;
  }

  // Проверяем, активна ли вкладка и открыт ли нужный чат
  const pageFocused = document.hasFocus();
  const isActiveChat = pageFocused && chatId === currentChatId;

  if (isActiveChat) {
    // Короткий деликатный звук для активного чата
    playActiveTabSound();
    console.log('🔔 Короткое уведомление для активного чата:', title, '-', body);
  } else {
    // Полное звуковое уведомление для неактивной вкладки или другого чата
    playNotificationSound();
    console.log('🔊 Звуковое уведомление:', title, '-', body);
  }
}

// Функция createNotification удалена - используем только звуковые уведомления

// Функция для отметки чата как прочитанного
async function markChatAsRead(chatId) {
  try {
    const response = await fetch(`https://api.chat-service-blazing-group.com/v2/widget/chats/${chatId}/read`, {
      method: 'PATCH',
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7',
        'authorization': `Bearer ${window.CHAT_BEARER}`,
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'x-socket-id': '944465183.436493150',
        'Referer': 'https://blazingboost.com/'
      }
    });

    if (response.ok) {
      console.log(`✅ Чат ${chatId} отмечен как прочитанный`);

      // Обновляем локальные данные чата
      const chat = allChats.find(c => c.chatId === chatId);
      if (chat) {
        chat.unreadCount = 0;
      }

      // Обновляем интерфейс списка чатов
      updateChatItemUnreadStatus(chatId);

    } else {
      console.warn(`❌ Ошибка отметки чата ${chatId} как прочитанного:`, response.status);
    }
  } catch (error) {
    console.error(`❌ Ошибка запроса read для чата ${chatId}:`, error.message);
  }
}

// Функция для обновления статуса непрочитанных сообщений конкретного чата в интерфейсе
function updateChatItemUnreadStatus(chatId) {
  const chatItems = document.querySelectorAll('.chat-item');
  for (const item of chatItems) {
    const onclickAttr = item.getAttribute('onclick');
    if (onclickAttr && onclickAttr.includes(`selectChat('${chatId}')`)) {
      // Убираем класс unread
      item.classList.remove('unread');

      // Убираем badge с количеством непрочитанных
      const unreadBadge = item.querySelector('.unread-badge');
      if (unreadBadge) {
        unreadBadge.remove();
      }

      console.log(`🔄 Обновлен интерфейс для чата ${chatId} - убраны индикаторы непрочитанных`);
      break;
    }
  }
}

// Функция для проверки, является ли сообщение системным о завершении заказа
function isOrderCompletionMessage(messageData) {
  const item = messageData.item || {};
  const author = item.author || {};
  const text = item.text || item.preview || '';

  // Проверяем автора: name должно быть "WoW War Within" и senderId должно быть пустым
  const isSystemMessage = author.name === 'WoW War Within' &&
                          (!item.senderId || item.senderId === '' ||
                           !author.senderId || author.senderId === '');

  // Проверяем текст сообщения
  const hasCompletionText = text.includes('Order completed') || text.includes('marked as completed');

  console.log(`🔍 Проверка сообщения: автор="${author.name}", senderId="${item.senderId || author.senderId}", текст="${text.substring(0, 50)}..."`);
  console.log(`🔍 Результат: isSystemMessage=${isSystemMessage}, hasCompletionText=${hasCompletionText}`);

  return isSystemMessage && hasCompletionText;
}

async function loadChatList() {
  try {
    const response = await fetch('/api/chats');
    const chats = await response.json();
    allChats = chats;

    // Подсчитываем чаты с channelId
    const chatsWithChannelId = chats.filter(chat => chat.channelId).length;
    console.log(`📋 Загружено ${chats.length} чатов, ${chatsWithChannelId} с предзагруженным channelId`);

    displayChatList(chats);
  } catch (error) {
    console.error('Ошибка загрузки списка чатов:', error);
    document.getElementById('chatList').innerHTML =
      '<div style="color: red; padding: 20px;">Ошибка загрузки чатов</div>';
  }
}

function displayChatList(chats) {
  const chatList = document.getElementById('chatList');
  chatList.innerHTML = chats.map(chat => {
    const apiIndicator = chat.fromApi ? '<span style="color: #28a745; font-weight: bold;">[API]</span> ' : '';

    let timeStr = '';
    if (chat.lastActivity) {
      const lastTime = new Date(chat.lastActivity * 1000);
      const now = new Date();
      const diffMs = now - lastTime;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);
      if (diffMins < 1) timeStr = 'сейчас';
      else if (diffMins < 60) timeStr = `${diffMins}м`;
      else if (diffHours < 24) timeStr = `${diffHours}ч`;
      else if (diffDays < 7) timeStr = `${diffDays}д`;
      else timeStr = lastTime.toLocaleDateString('ru-RU', { day: '2-digit', month: '2-digit' });
    }

    let lastMessageText = '';
    if (chat.lastMessage) {
      lastMessageText = chat.lastMessage.text;
      if (lastMessageText.length > 40) lastMessageText = lastMessageText.substring(0, 40) + '...';
    }

    const unreadClass = chat.unreadCount > 0 ? 'unread' : '';
    const unreadBadge = chat.unreadCount > 0 ? `<span class="unread-badge">${chat.unreadCount}</span>` : '';

    return `<div class="chat-item ${unreadClass}" onclick="selectChat('${chat.chatId}')">
      ${unreadBadge}
      <div class="chat-title">
        ${apiIndicator}${chat.serviceName}
        <span class="chat-time">${timeStr}</span>
      </div>
      ${lastMessageText ? `<div class="chat-last-message">${lastMessageText}</div>` : ''}
      <div class="chat-service-uid">${chat.serviceUids.filter(uid => uid).join(', ')}</div>
    </div>`;
  }).join('');
}

async function selectChat(chatId) {
  currentChatId = chatId;
  document.querySelectorAll('.chat-item').forEach(item => item.classList.remove('active'));
  event.target.closest('.chat-item').classList.add('active');

  // Проверяем, есть ли у чата непрочитанные сообщения
  const selectedChat = allChats.find(chat => chat.chatId === chatId);
  if (selectedChat && selectedChat.unreadCount > 0) {
    console.log(`📖 Отмечаем чат ${chatId} как прочитанный (было ${selectedChat.unreadCount} непрочитанных)`);
    markChatAsRead(chatId);
  }

  const chatHeader = document.getElementById('chatHeader');
  const chatTitle = document.getElementById('chatTitle');
  const chatMeta = document.getElementById('chatMeta');
  const messageInputContainer = document.getElementById('messageInputContainer');

  chatHeader.style.display = 'flex';
  chatTitle.textContent = 'Загрузка...';
  chatMeta.textContent = '';

  // Показываем поле ввода сообщений
  messageInputContainer.style.display = 'block';

  await loadMessages(chatId);
  socket.emit('join-chat', chatId);

  // Фокус на поле ввода
  setTimeout(() => {
    document.getElementById('messageInput').focus();
  }, 100);
}

async function loadMessages(chatId) {
  const messagesContainer = document.getElementById('messagesContainer');
  messagesContainer.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
  try {
    const response = await fetch(`/api/chat/${chatId}`);
    const data = await response.json();
    if (data.error) {
      messagesContainer.innerHTML = `<div style="color: red; text-align: center; padding: 20px;">${data.error}</div>`;
      return;
    }

    // Пытаемся получить channelId для этого чата
    try {
      const channelResponse = await fetch(`/api/chat/${chatId}/channel`);
      const channelData = await channelResponse.json();

      if (channelData.channelId) {
        currentChannelId = channelData.channelId;
        console.log('Получен channelId для чата:', chatId, '→', currentChannelId);
      } else {
        currentChannelId = 'fallback';
        console.log('channelId не найден для чата:', chatId, ', используем fallback');
      }
    } catch (error) {
      console.warn('Ошибка получения channelId:', error);
      currentChannelId = 'fallback';
    }

    displayMessages(data.items || [], chatId);
    lastMessageCount = data.items ? data.items.length : 0;
    updateChatHeader(chatId, data.items ? data.items.length : 0);
  } catch (error) {
    console.error('Ошибка загрузки сообщений:', error);
    messagesContainer.innerHTML = '<div style="color: red; text-align: center; padding: 20px;">Ошибка загрузки сообщений</div>';
  }
}

function displayMessages(messages, chatIdParam) {
  const chatId = chatIdParam || currentChatId;
  const messagesContainer = document.getElementById('messagesContainer');
  if (!messages || messages.length === 0) {
    messagesContainer.innerHTML = '<div class="empty-state"><i class="fas fa-inbox"></i><h3>Сообщений пока нет</h3></div>';
    return;
  }

  const seen = new Set();
  const sorted = messages.slice().sort((a, b) => {
    const ta = (a.item && a.item.timestamp) ? a.item.timestamp : 0;
    const tb = (b.item && b.item.timestamp) ? b.item.timestamp : 0;
    if (ta !== tb) return ta - tb;
    const ka = getMessageKeyFromItem(a.item || {});
    const kb = getMessageKeyFromItem(b.item || {});
    return ka < kb ? -1 : ka > kb ? 1 : 0;
  });

  const html = [];
  const perChatSeen = getSeenSet(chatId);
  for (const msg of sorted) {
    const item = msg.item || {};
    const key = getMessageKeyFromItem(item);
    if (seen.has(key)) continue; // локально в этом наборе
    seen.add(key);
    perChatSeen.add(key); // запоминаем как уже виденное в этом чате

    const time = new Date((item.timestamp || 0) * 1000).toLocaleString('ru-RU');
    const sender = item.author?.name || 'Система';
    const content = item.text || item.preview || '[без текста]';
    const type = msg.type === 'system-message' ? 'system' : 'user';
    const avatar = sender.charAt(0).toUpperCase();
    const ts = item.timestamp || 0;
    html.push(`
      <div class="message ${type}" data-message-key="${key}" data-ts="${ts}">
        <div class="message-avatar">${avatar}</div>
        <div class="message-content">
          <div class="message-header">
            <div class="message-sender">${sender}</div>
            <div class="message-time">${time}</div>
          </div>
          <div class="message-text">${content.replace(/\n/g, '<br>')}</div>
        </div>
      </div>
    `);
  }
  messagesContainer.innerHTML = html.join('');
  if (autoScroll) messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function updateChatHeader(chatId, messageCount) {
  const chatTitle = document.getElementById('chatTitle');
  const chatMeta = document.getElementById('chatMeta');
  const chatItems = document.querySelectorAll('.chat-item');
  let chatInfo = null;
  chatItems.forEach(item => {
    if (item.onclick.toString().includes(chatId)) {
      const titleElement = item.querySelector('.chat-title');
      const serviceUidElement = item.querySelector('.chat-service-uid');
      if (titleElement) {
        const title = titleElement.textContent.trim();
        const serviceUid = serviceUidElement ? serviceUidElement.textContent.trim() : '';
        chatInfo = { title, serviceUid };
      }
    }
  });
  if (chatInfo) {
    chatTitle.textContent = chatInfo.title;
    chatMeta.textContent = `${messageCount} сообщений • ${chatInfo.serviceUid}`;
  } else {
    chatTitle.textContent = 'Чат';
    chatMeta.textContent = `${messageCount} сообщений`;
  }
}

// Функция отправки сообщения
async function sendMessage(text) {
  if (!currentChatId || !text.trim()) return;

  const sendButton = document.getElementById('sendButton');
  const messageInput = document.getElementById('messageInput');

  try {
    sendButton.disabled = true;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Подготавливаем данные для отправки
    const messageData = {
      text: text.trim(),
      chatId: currentChatId // Всегда отправляем chatId
    };

    // Добавляем channelId если есть (как в оригинальном запросе - ОБА поля)
    if (currentChannelId && currentChannelId !== 'fallback') {
      messageData.channelId = currentChannelId;
      console.log('Отправляем с channelId:', currentChannelId, 'и chatId:', currentChatId);
    } else {
      console.log('Отправляем только с chatId:', currentChatId);
    }

    console.log('Данные для отправки:', messageData);

    const response = await fetch('/api/send-message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(messageData)
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Неизвестная ошибка' }));
      console.error('Ошибка ответа сервера:', response.status, error);
      throw new Error(error.error || `Ошибка ${response.status}: ${error.details || 'Неизвестная ошибка'}`);
    }

    // Очищаем поле ввода
    messageInput.value = '';
    messageInput.style.height = 'auto';

    console.log('Сообщение отправлено:', text);

  } catch (error) {
    console.error('Ошибка отправки сообщения:', error);
    alert('Ошибка отправки сообщения: ' + error.message);
  } finally {
    sendButton.disabled = false;
    sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
    messageInput.focus();
  }
}



document.getElementById('messagesContainer').addEventListener('scroll', function() {
  const container = this;
  const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 100;
  autoScroll = isAtBottom;
  if (isAtBottom) document.getElementById('newMessageIndicator').style.display = 'none';
});

function scrollToBottom() {
  const container = document.getElementById('messagesContainer');
  container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' });
}

document.getElementById('newMessageIndicator').addEventListener('click', () => {
  scrollToBottom();
  document.getElementById('newMessageIndicator').style.display = 'none';
  autoScroll = true;
});

socket.on('new-message', (data) => {
  const item = data.message.item || {};
  const key = getMessageKeyFromItem(item);

  // Жесткая дедупликация на уровне кэша увиденных для чата
  const perChatSeen = getSeenSet(data.chatId);
  if (perChatSeen.has(key)) {
    // Уже видели — не дублируем, не уведомляем
    return;
  }

  const sender = item.author?.name || 'Система';
  const content = item.text || item.preview || '[без текста]';

  // Проверяем, является ли это системным сообщением о завершении заказа
  const isOrderCompletion = isOrderCompletionMessage(data.message);
  if (isOrderCompletion) {
    console.log(`🎯 Обнаружено сообщение о завершении заказа в чате ${data.chatId}: ${content}`);
    // Отмечаем чат как прочитанный
    markChatAsRead(data.chatId);
    // Не воспроизводим уведомление для таких сообщений
    perChatSeen.add(key);
    return;
  }

  if (data.chatId === currentChatId) {
    const added = insertMessageInOrder(data.message);
    if (added) {
      perChatSeen.add(key);
      if (autoScroll) {
        const messagesContainer = document.getElementById('messagesContainer');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      } else {
        const indicator = document.getElementById('newMessageIndicator');
        indicator.style.display = 'block';
      }
      lastMessageCount++;
      updateChatHeader(currentChatId, lastMessageCount);
    } else {
      // Даже если не вставилось (дубль DOM), считаем увиденным, чтобы не уведомлять
      perChatSeen.add(key);
    }
  } else {
    // Другой чат — уведомляем только если это не дубль, не в grace-периоде и не наше сообщение
    perChatSeen.add(key);

    // Проверяем, не является ли это нашим сообщением
    if (!isOurMessage(data.message)) {
      const chatInfo = allChats.find(chat => chat.chatId === data.chatId);
      const chatName = chatInfo ? chatInfo.serviceName : 'Неизвестный чат';
      const serviceUid = chatInfo && chatInfo.serviceUids ? chatInfo.serviceUids[0] : '';
      showNotification(
        `💬 ${chatName}${serviceUid ? ' (' + serviceUid + ')' : ''}`,
        `${sender}: ${content.length > 60 ? content.substring(0, 60) + '...' : content}`,
        data.chatId,
        data.message
      );
    } else {
      console.log('Пропускаем уведомление для собственного сообщения:', sender);
    }
  }

  // Не дублируем уведомление для текущего чата — отдельного вызова больше нет

  // Синхронизируем список чатов
  loadChatList().catch(() => {});
});

document.getElementById('newMessageIndicator').addEventListener('click', function() {
  const messagesContainer = document.getElementById('messagesContainer');
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
  autoScroll = true;
  this.style.display = 'none';
});

async function filterChats(searchTerm) {
  if (!searchTerm.trim()) { displayChatList(allChats); return; }
  const filtered = allChats.filter(chat => {
    const searchLower = searchTerm.toLowerCase();
    return chat.chatId.toLowerCase().includes(searchLower) ||
           chat.serviceUids.some(uid => uid && uid.toLowerCase().includes(searchLower)) ||
           chat.serviceName.toLowerCase().includes(searchLower);
  });
  if (searchTerm.match(/^S[A-Z0-9]+$/i)) {
    try {
      const response = await fetch('/api/search/' + searchTerm);
      const result = await response.json();
      if (result.chatId && !result.error) {
        const apiChat = {
          chatId: result.chatId,
          serviceName: result.info.serviceName,
          serviceUids: result.info.serviceUids,
          lastMessage: null,
          lastActivity: Date.now() / 1000,
          unreadCount: 0,
          fromApi: true
        };
        const uniqueChats = [apiChat, ...filtered.filter(chat => chat.chatId !== result.chatId)];
        displayChatList(uniqueChats);
        return;
      }
    } catch (e) { console.error('Ошибка API поиска:', e); }
  }
  displayChatList(filtered);
}

document.getElementById('searchInput').addEventListener('input', function() {
  filterChats(this.value);
});

async function initialize() {
  await requestNotificationPermission();
  await loadChatList();
  const urlParams = new URLSearchParams(window.location.search);
  const chatIdFromUrl = urlParams.get('chat');
  if (chatIdFromUrl) {
    setTimeout(() => {
      const chatItems = document.querySelectorAll('.chat-item');
      for (const item of chatItems) {
        // Ищем по onclick атрибуту, который содержит chatId
        const onclickAttr = item.getAttribute('onclick');
        if (onclickAttr && onclickAttr.includes(`selectChat('${chatIdFromUrl}')`)) {
          item.click();
          break;
        }
      }
    }, 500);
  }
}

initialize();

setInterval(() => {
  loadChatList().catch(()=>{});
  if (currentChatId) { loadMessages(currentChatId).catch(()=>{}); }
}, 10 * 60 * 1000);

let originalTitle = document.title;
let unreadCount = 0;
function updatePageTitle() {
  document.title = unreadCount > 0 ? `(${unreadCount}) ${originalTitle}` : originalTitle;
}
window.addEventListener('focus', () => { unreadCount = 0; updatePageTitle(); });

document.addEventListener('DOMContentLoaded', () => {
  const indicator = document.querySelector('.online-indicator');
  if (indicator) indicator.addEventListener('click', handleIndicatorClick);

  // Обработчики для отправки сообщений
  const messageForm = document.getElementById('messageForm');
  const messageInput = document.getElementById('messageInput');
  const sendButton = document.getElementById('sendButton');

  if (messageForm && messageInput && sendButton) {
    // Отправка формы
    messageForm.addEventListener('submit', (e) => {
      e.preventDefault();
      const text = messageInput.value.trim();
      if (text) {
        sendMessage(text);
      }
    });

    // Автоматическое изменение высоты textarea
    messageInput.addEventListener('input', () => {
      messageInput.style.height = 'auto';
      messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';

      // Включаем/выключаем кнопку отправки
      sendButton.disabled = !messageInput.value.trim();
    });

    // Отправка по Enter
    messageInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        const text = messageInput.value.trim();
        if (text) {
          sendMessage(text);
        }
      }
    });

    // Начальное состояние кнопки
    sendButton.disabled = true;
  }
});
