// Тестовый скрипт для проверки API отправки сообщений
async function testAPI() {
  try {
    // Динамический импорт node-fetch для совместимости
    const fetch = (await import('node-fetch')).default;
    
    console.log('🚀 Отправка тестового запроса к API...');
    
    const response = await fetch('http://localhost:61692/api/send-by-service-id', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        serviceId: 'S254AB69FB',
        text: '👍'
      })
    });

    console.log(`📡 Статус ответа: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Ошибка: ${errorText}`);
      return;
    }

    const result = await response.json();
    console.log('✅ Успешный ответ:', JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('❌ Ошибка при отправке запроса:', error.message);
  }
}

// Запускаем тест
testAPI();
