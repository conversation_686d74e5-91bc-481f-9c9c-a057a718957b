# 🚀 Запуск всех сервисов через PM2

Этот проект настроен для запуска всех сервисов одной командой через PM2.

## 📦 Сервисы

- **monitor** - основной монитор чатов
- **chat-web** - веб-интерфейс для просмотра чатов (порт 3000)
- **orders-monitor** - монитор заказов с Telegram уведомлениями

## 🛠 Установка PM2

Если PM2 не установлен:

```bash
npm install -g pm2
```

## 🚀 Быстрый запуск

### Windows (PowerShell)
```powershell
.\start-all.ps1
```

### Linux/Mac
```bash
./start-all.sh
```

### Или через npm
```bash
npm run pm2:start
```

## 📋 Управление сервисами

### Основные команды
```bash
# Запуск всех сервисов
npm run pm2:start

# Остановка всех сервисов
npm run pm2:stop

# Перезапуск всех сервисов
npm run pm2:restart

# Плавная перезагрузка (без даунтайма)
npm run pm2:reload

# Удаление всех сервисов из PM2
npm run pm2:delete

# Статус сервисов
npm run pm2:status

# Просмотр логов всех сервисов
npm run pm2:logs

# Мониторинг в реальном времени
npm run pm2:monit
```

### Управление отдельными сервисами
```bash
# Перезапуск конкретного сервиса
pm2 restart monitor
pm2 restart chat-web
pm2 restart orders-monitor

# Просмотр логов конкретного сервиса
pm2 logs monitor
pm2 logs chat-web
pm2 logs orders-monitor

# Остановка конкретного сервиса
pm2 stop monitor
pm2 stop chat-web
pm2 stop orders-monitor
```

## 📊 Мониторинг

### Веб-интерфейс PM2
```bash
pm2 web
```
Откроет веб-интерфейс на http://localhost:9615

### Статус в терминале
```bash
pm2 status
```

### Мониторинг ресурсов
```bash
pm2 monit
```

## 📝 Логи

Логи сохраняются в папке `./logs/`:

- `monitor-*.log` - логи основного монитора
- `chat-web-*.log` - логи веб-интерфейса
- `orders-monitor-*.log` - логи монитора заказов

### Просмотр логов
```bash
# Все логи в реальном времени
pm2 logs

# Логи конкретного сервиса
pm2 logs monitor --lines 100

# Очистка логов
pm2 flush
```

## 🌐 Доступные интерфейсы

После запуска доступны:

- **Chat Web Interface**: http://localhost:3000
- **PM2 Web Interface**: http://localhost:9615 (после `pm2 web`)

## 🔧 Конфигурация

Настройки PM2 находятся в файле `ecosystem.config.js`. Можно изменить:

- Количество инстансов
- Лимиты памяти
- Переменные окружения
- Пути к логам

## 🆘 Устранение проблем

### Сервисы не запускаются
```bash
# Проверить статус
pm2 status

# Посмотреть ошибки
pm2 logs --err

# Перезапустить проблемный сервис
pm2 restart <service-name>
```

### Очистка и перезапуск
```bash
# Остановить и удалить все процессы
pm2 delete all

# Запустить заново
npm run pm2:start
```

### Автозапуск при загрузке системы
```bash
# Сохранить текущую конфигурацию
pm2 save

# Настроить автозапуск
pm2 startup

# Следовать инструкциям в выводе команды
```
