const fs = require('fs');

// Копируем необходимые функции из orders-monitor.js
function getItemsArray(container) {
  if (!container) return [];
  if (Array.isArray(container)) return container;
  if (Array.isArray(container.items)) return container.items;
  return [];
}

function getValueByName(items, name) {
  const it = items.find(x => (x.name || x.title) === name);
  return it ? (it.value || it.body) : undefined;
}

function parseDateTime(yyyymmdd_hhmm) {
  // '2025/08/25 16:00' -> { date: '25.08.2025', time: '16:00' }
  if (!yyyymmdd_hhmm) return { date: '', time: '' };
  const [dateStr, time] = yyyymmdd_hhmm.split(/\s+/);
  const [y, m, d] = dateStr.split('/');
  const ddmmyyyy = [d, m, y].join('.');
  return { date: ddmmyyyy, time: time || '' };
}

function extractPayout(details) {
  const p = details?.servicePurchaseDetails?.payoutInfo || details?.payoutInfo || '';
  if (typeof p !== 'string') return '';
  const m = p.match(/(\d+[.,]\d+)/);
  if (!m) return '';
  return m[1].replace('.', ',');
}

function isUnsavedRaidersValue(value, difficulty = '') {
  if (!value) return false;
  const v = String(value).trim();
  // Извлекаем первое число до дефиса/пробела/скобки
  const m = v.match(/(\d+)/);
  if (!m) return false;
  const first = parseInt(m[1], 10);
  if (Number.isNaN(first)) return false;

  // Для Normal сложности порог выше - больше 13
  // Для остальных сложностей - больше 7
  const threshold = difficulty === 'Normal' ? 13 : 7;
  return first > threshold;
}

function formatReadable(details, chatId = null) {
  const serviceUid = details?.serviceUid || details?.service?.serviceUid || '';
  const serviceName = details?.service?.name || '';

  const attributesItems = getItemsArray(details?.attributes);
  const extraOptionsItems = getItemsArray(details?.extraOptions);
  const postItems = getItemsArray(details?.postPurchaseInfo) // object with items or array
    .concat(getItemsArray(details?.postPurchaseInfo?.items));

  const difficulty = getValueByName(attributesItems, 'Difficulty') || getValueByName(extraOptionsItems, 'Difficulty') || '';
  const runType = getValueByName(attributesItems, 'Run Type') || getValueByName(extraOptionsItems, 'Run Type') || '';
  const lootType = getValueByName(attributesItems, 'Loot Type') || getValueByName(extraOptionsItems, 'Loot Type') || '';
  const premiumLootRunType = getValueByName(attributesItems, 'Premium Loot Run Type') || getValueByName(extraOptionsItems, 'Premium Loot Run Type') || '';
  const unsavedRaiders = getValueByName(attributesItems, 'Unsaved Raiders') || getValueByName(extraOptionsItems, 'Unsaved Raiders') || '';
  const lootDistribution = getValueByName(attributesItems, 'Loot Distribution') || getValueByName(extraOptionsItems, 'Loot Distribution') || '';
  const completionMethod = getValueByName(attributesItems, 'Completion Method') || getValueByName(extraOptionsItems, 'Completion Method') ||
                        getValueByName(attributesItems, 'Completion Type') || getValueByName(extraOptionsItems, 'Completion Type') ||
                        getValueByName(attributesItems, 'Mode') || getValueByName(extraOptionsItems, 'Mode') || '';
  const amountOfBosses = getValueByName(attributesItems, 'Amount of Bosses') || getValueByName(extraOptionsItems, 'Amount of Bosses') || '';
  const dateTimeRaw = getValueByName(attributesItems, 'Date & Time') || getValueByName(extraOptionsItems, 'Date & Time') || '';
  const { date, time } = parseDateTime(dateTimeRaw);
  let characterProfile = getValueByName(postItems, 'Character Profile') || '';
  if (typeof characterProfile === 'string') characterProfile = characterProfile.replace(/\\\//g, '/');
  const price = extractPayout(details);

  const needUnsaved = isUnsavedRaidersValue(unsavedRaiders, difficulty);

  let task;
  if (serviceName === 'Raid Calendar Run') {
    // Не показываем "Loot Sharing Runs" в названии задачи
    const shouldShowLootType = lootType && lootType !== 'Loot Sharing Runs';
    const base = `${difficulty || ''}${difficulty ? ' ' : ''}${runType || ''}${shouldShowLootType ? ` (${lootType})` : ''}`.trim();
    task = `${needUnsaved ? 'Unsaved ' : ''}${base}`.trim();
  } else {
    task = `${needUnsaved ? 'Unsaved ' : ''}${serviceName}`.trim();
  }

  const lines = [
    `1. orderid: ${serviceUid}`,
    `2. task: ${task}`
  ];

  // Добавляем специальное описание для Blazing Battle Package
  if (serviceName === 'Blazing Battle Package') {
    lines.push(`   -Manaforge Heroic + Manaforge Heroic + Manaforge Normal`);
  }

  // Добавляем детали Premium Loot Run Type и Unsaved Raiders если есть
  if (premiumLootRunType) {
    lines.push(`   -Premium Loot Run Type - ${premiumLootRunType}`);
    // Показываем Unsaved Raiders только если есть Premium Loot Run Type
    if (unsavedRaiders) {
      lines.push(`   -Unsaved Raiders ${unsavedRaiders}`);
    }
  }
  
  // Обрабатываем случай когда Loot Type = "Premium Loot Run"
  if (lootType === 'Premium Loot Run') {
    if (unsavedRaiders) {
      lines.push(`   -Unsaved Raiders ${unsavedRaiders}`);
    }
    if (lootDistribution) {
      // Заменяем "Full Monopoly" на "Full Priority"
      const displayDistribution = lootDistribution === 'Full Monopoly' ? 'Full Priority' : lootDistribution;
      lines.push(`   -${displayDistribution}`);
    }
  }

  // Добавляем Amount of Bosses если есть
  if (amountOfBosses) {
    lines.push(`   -${amountOfBosses}`);
  }

  // Ищем поле с Manaforge в extraOptions
  const manaforgeOption = extraOptionsItems.find(item => 
    item.name && item.name.includes('Manaforge') && item.value === true
  );
  if (manaforgeOption) {
    lines.push(`   -${manaforgeOption.name}`);
  }

  lines.push(
    `3. date:  ${date}`,
    `   time:  ${time}`,
    `4. character: ${characterProfile}`,
    `5. price: ${price}`
  );

  // Добавляем Completion Method всегда (по умолчанию сообщение об уточнении)
  const displayCompletionMethod = completionMethod || 'Селфплей или пилот не найдено информации, уточните';
  lines.push(`6. ${displayCompletionMethod}`);

  lines.push('');

  // Добавляем ссылку на чат в веб-интерфейсе, если есть chatId
  if (chatId) {
    const webPort = process.env.CHAT_WEB_PORT || 61692;
    const chatWebUrl = `http://138.201.175.112:${webPort}/?chat=${chatId}`;
    lines.push(`🔗 Чат: ${chatWebUrl}`);
    lines.push('');
  }

  return lines.join('\n');
}

// Читаем тестовые данные
const testData = fs.readFileSync('testorders.ndjson', 'utf8')
  .split('\n')
  .filter(line => line.trim())
  .map(line => JSON.parse(line));

console.log('='.repeat(80));
console.log('ТЕСТ ФОРМАТИРОВАНИЯ ЗАКАЗОВ');
console.log('='.repeat(80));

// Обрабатываем каждый заказ
testData.forEach((orderData, index) => {
  const details = orderData.details;
  const orderId = details.id;
  
  console.log(`\n📋 ЗАКАЗ ${index + 1} (ID: ${orderId})`);
  console.log('-'.repeat(50));
  
  const formatted = formatReadable(details, 'test-chat-id-123');
  console.log(formatted);
  
  console.log('-'.repeat(50));
});

console.log('\n✅ Тест завершен!');
