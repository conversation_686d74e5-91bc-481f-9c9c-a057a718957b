const express = require('express');
const path = require('path');
const http = require('http');
const socketIo = require('socket.io');
const session = require('express-session');
const FileStore = require('session-file-store')(session);
const FormData = require('form-data');
require('dotenv').config();

const { getChatMessages, listChats, findChatByServiceUid, getChatsFromAPI } = require('./chat-viewer');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);
const PORT = process.env.CHAT_WEB_PORT || 3000;

// Настройка сессий с file store
app.use(session({
  store: new FileStore({
    path: './sessions',
    ttl: 864000, // 24 часа в секундах
    retries: 5,
    factor: 1,
    minTimeout: 50,
    maxTimeout: 100
  }),
  secret: process.env.SESSION_SECRET || 'blazing-boost-secret-key-change-in-production',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false, // Установить true для HTTPS в продакшене
    maxAge: 24 * 60 * 60 * 1000 // 24 часа
  }
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware для проверки авторизации
function requireAuth(req, res, next) {
  if (req.session.authenticated) {
    return next();
  }

  // Если это AJAX запрос, возвращаем JSON
  if (req.xhr || req.headers.accept.indexOf('json') > -1) {
    return res.status(401).json({ error: 'Требуется авторизация' });
  }

  // Иначе перенаправляем на страницу входа
  res.redirect('/login');
}

// Middleware для проверки localhost доступа
function requireLocalhost(req, res, next) {
  const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  const isLocalhost = clientIP === '127.0.0.1' ||
                     clientIP === '::1' ||
                     clientIP === '::ffff:127.0.0.1' ||
                     clientIP === 'localhost';

  if (isLocalhost) {
    next();
  } else {
    console.warn(`❌ Отклонен запрос с IP: ${clientIP} (только localhost разрешен)`);
    res.status(403).json({ error: 'Доступ разрешен только с localhost' });
  }
}

// Публичная папка (только для статических ресурсов без чувствительных данных)
app.use('/public', express.static(path.join(__dirname, 'public')));

// Защищенная папка (только для авторизованных пользователей)
app.use('/protected', requireAuth, express.static(path.join(__dirname, 'protected')));

// Хранилище активных чатов
const activeChats = new Map();

// Хранилище channelId для чатов
const chatChannels = new Map();

// Страница входа
app.get('/login', (req, res) => {
  if (req.session.authenticated) {
    return res.redirect('/');
  }

  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Вход - BlazingBoost Live Chat</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .login-container {
          background: white;
          padding: 40px;
          border-radius: 12px;
          box-shadow: 0 10px 30px rgba(0,0,0,0.2);
          width: 100%;
          max-width: 400px;
        }
        .login-header {
          text-align: center;
          margin-bottom: 30px;
        }
        .login-header h1 {
          color: #2c3e50;
          margin-bottom: 10px;
        }
        .login-header p {
          color: #7f8c8d;
          font-size: 14px;
        }
        .form-group {
          margin-bottom: 20px;
        }
        .form-group label {
          display: block;
          margin-bottom: 8px;
          color: #2c3e50;
          font-weight: 500;
        }
        .form-group input {
          width: 100%;
          padding: 12px;
          border: 2px solid #e0e0e0;
          border-radius: 6px;
          font-size: 16px;
          transition: border-color 0.3s ease;
        }
        .form-group input:focus {
          outline: none;
          border-color: #667eea;
        }
        .login-btn {
          width: 100%;
          padding: 12px;
          background: #667eea;
          color: white;
          border: none;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: background 0.3s ease;
        }
        .login-btn:hover {
          background: #5a6fd8;
        }
        .error {
          background: #fee;
          color: #c33;
          padding: 12px;
          border-radius: 6px;
          margin-bottom: 20px;
          border: 1px solid #fcc;
        }
      </style>
    </head>
    <body>
      <div class="login-container">
        <div class="login-header">
          <h1>🔒 Вход в систему</h1>
          <p>BlazingBoost Live Chat Monitor</p>
        </div>
        ${req.query.error ? '<div class="error">Неверный пароль</div>' : ''}
        <form method="POST" action="/login">
          <div class="form-group">
            <label for="password">Пароль:</label>
            <input type="password" id="password" name="password" required>
          </div>
          <button type="submit" class="login-btn">Войти</button>
        </form>
      </div>
    </body>
    </html>
  `);
});

// Обработка входа
app.post('/login', (req, res) => {
  const { password } = req.body;
  const correctPassword = process.env.ADMIN_PASSWORD || 'blazing123';

  if (password === correctPassword) {
    req.session.authenticated = true;
    res.redirect('/');
  } else {
    res.redirect('/login?error=1');
  }
});

// Выход
app.get('/logout', (req, res) => {
  req.session.destroy();
  res.redirect('/login');
});

// Кэш для недоступных чатов (чтобы не спамить запросами)
const inaccessibleChats = new Set();
const chatCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 минут

// Главная страница (защищенная)
app.get('/', requireAuth, (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>BlazingBoost Live Chat</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
      <script>
        // Передаем ID нашего аккаунта из переменной окружения
        window.OUR_SELLER_ID = '${process.env.OUR_SELLER_ID || '4a614f54e0ead57825c22d861de7bf7d'}';
        // Передаем Bearer токен для API запросов
        window.CHAT_BEARER = '${process.env.CHAT_BEARER || ''}';
      </script>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body { 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: #f5f5f5;
          height: 100vh;
          display: flex;
        }
        
        .sidebar {
          width: 300px;
          background: white;
          border-right: 1px solid #e0e0e0;
          display: flex;
          flex-direction: column;
          height: 100vh;
          position: relative;
        }
        
        .sidebar-header {
          padding: 20px;
          border-bottom: 1px solid #e0e0e0;
          background: #2c3e50;
          color: white;
          position: relative;
        }

        .logout-btn {
          position: absolute;
          bottom: 10px;
          right: 10px;
          background: #e74c3c;
          color: white;
          border: none;
          padding: 6px 12px;
          border-radius: 4px;
          font-size: 11px;
          cursor: pointer;
          text-decoration: none;
          transition: background 0.2s ease;
          z-index: 10;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .logout-btn:hover {
          background: #c0392b;
          transform: translateY(-1px);
        }

        .sidebar-header h1 {
          font-size: 18px;
          margin-bottom: 5px;
        }

        .sidebar-header p {
          font-size: 12px;
          opacity: 0.8;
        }

        .online-indicator {
          position: absolute;
          top: 15px;
          right: 15px;
          width: 12px;
          height: 12px;
          background: #28a745;
          border-radius: 50%;
          border: 2px solid white;
          animation: pulse 2s infinite;
          cursor: pointer;
        }

        .notification-btn {
          background: #ffc107;
          color: #212529;
          border: none;
          padding: 8px 16px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
          cursor: pointer;
          margin-top: 10px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 6px;
        }

        .notification-btn:hover {
          background: #e0a800;
          transform: translateY(-1px);
        }

        .notification-btn.enabled {
          background: #28a745;
          color: white;
        }

        .notification-btn.enabled:hover {
          background: #218838;
        }

        .test-notification-btn {
          background: #17a2b8;
          color: white;
          border: none;
          padding: 6px 12px;
          border-radius: 15px;
          font-size: 11px;
          font-weight: 500;
          cursor: pointer;
          margin-top: 8px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .test-notification-btn:hover {
          background: #138496;
          transform: translateY(-1px);
        }
         
         .search-box {
           padding: 15px;
           border-bottom: 1px solid #e0e0e0;
         }
         
         .search-input {
           width: 100%;
           padding: 8px 12px;
           border: 1px solid #ddd;
           border-radius: 6px;
           font-size: 14px;
         }
         
         .search-input:focus {
           outline: none;
           border-color: #007bff;
           box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
         }
        
        .chat-list {
          flex: 1;
          overflow-y: auto;
          padding: 10px;
        }
        
        .chat-item {
          padding: 12px 15px;
          border-radius: 8px;
          margin-bottom: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          border: 1px solid #e0e0e0;
          background: white;
          position: relative;
        }

        .chat-item:hover {
          background: #f8f9fa;
          border-color: #007bff;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0,123,255,0.1);
        }

        .chat-item.active {
          background: #007bff;
          color: white;
          border-color: #007bff;
          box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        .chat-item.active .chat-title {
          color: white;
        }

        .chat-item.active .chat-meta,
        .chat-item.active .chat-orders,
        .chat-item.active .chat-last-message,
        .chat-item.active .chat-time {
          color: rgba(255,255,255,0.9);
        }

        .chat-item.active .chat-service-uid {
          color: #ffffff;
          font-weight: 700;
        }

        /* Переопределяем стили для активного чата с непрочитанными сообщениями */
        .chat-item.active.unread {
          background: #007bff !important;
          color: white !important;
          border-color: #007bff !important;
          border-left: 4px solid #28a745;
        }

        .chat-item.active.unread .chat-title,
        .chat-item.active.unread .chat-meta,
        .chat-item.active.unread .chat-orders,
        .chat-item.active.unread .chat-last-message,
        .chat-item.active.unread .chat-time,
        .chat-item.active.unread .chat-service-uid {
          color: white !important;
        }

        .chat-item.active.unread .chat-service-uid {
          font-weight: 700 !important;
        }

        .chat-item.unread {
          border-left: 4px solid #28a745;
          background: #f8fff9;
        }

        .chat-item.unread .chat-title {
          font-weight: 700;
        }

        .chat-title {
          font-weight: 600;
          margin-bottom: 4px;
          font-size: 14px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-right: 30px; /* Добавляем отступ справа для badge */
        }

        .chat-time {
          font-size: 11px;
          color: #888;
          font-weight: normal;
        }

        .chat-last-message {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 250px;
        }

        .chat-meta {
          font-size: 11px;
          color: #888;
          margin-bottom: 2px;
        }

        .chat-orders {
          font-size: 10px;
          color: #aaa;
          margin-bottom: 2px;
        }

        .chat-service-uid {
          font-size: 13px;
          color: #007bff;
          font-family: monospace;
          font-weight: 600;
          margin-top: 2px;
        }

        .unread-badge {
          background: #dc3545;
          color: white;
          border-radius: 12px;
          padding: 3px 8px;
          font-size: 11px;
          font-weight: bold;
          min-width: 20px;
          text-align: center;
          position: absolute;
          top: 35px;
          right: 10px;
          box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
          animation: pulse 2s infinite;
          z-index: 10;
        }

        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.1); }
          100% { transform: scale(1); }
        }
        
        .main-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          height: 100vh;
        }
        
        .chat-header {
          padding: 20px;
          background: white;
          border-bottom: 1px solid #e0e0e0;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        
        .chat-info h2 {
          font-size: 18px;
          margin-bottom: 5px;
        }
        
        .chat-status {
          display: flex;
          align-items: center;
          gap: 10px;
        }
        
        .status-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #28a745;
          animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
        
        .messages-container {
          flex: 1;
          background: #f8f9fa;
          overflow-y: auto;
          padding: 20px;
          margin-bottom: 80px; /* Место для поля ввода */
        }

        .message-input-container {
          position: fixed;
          bottom: 0;
          right: 0;
          left: 300px; /* Ширина сайдбара */
          background: white;
          border-top: 1px solid #e0e0e0;
          padding: 15px 20px;
          display: none; /* Скрыто по умолчанию */
          z-index: 1000;
        }

        .message-input-form {
          display: flex;
          gap: 10px;
          align-items: flex-end;
        }

        .message-input {
          flex: 1;
          min-height: 40px;
          max-height: 120px;
          padding: 10px 15px;
          border: 2px solid #e0e0e0;
          border-radius: 20px;
          font-size: 14px;
          font-family: inherit;
          resize: none;
          outline: none;
          transition: border-color 0.2s ease;
        }

        .message-input:focus {
          border-color: #007bff;
        }

        .send-button {
          background: #007bff;
          color: white;
          border: none;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
          font-size: 16px;
        }

        .send-button:hover:not(:disabled) {
          background: #0056b3;
          transform: scale(1.05);
        }

        .send-button:disabled {
          background: #ccc;
          cursor: not-allowed;
          transform: none;
        }

        @media (max-width: 768px) {
          .message-input-container {
            left: 250px;
          }
        }
        
        .message {
          margin-bottom: 20px;
          display: flex;
          align-items: flex-start;
          gap: 12px;
        }
        
        .message-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #007bff;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          font-size: 14px;
        }
        
        .message.system .message-avatar {
          background: #6c757d;
        }
        
        .message-content {
          flex: 1;
          background: white;
          padding: 12px 16px;
          border-radius: 12px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          max-width: 70%;
        }
        
        .message.system .message-content {
          background: #e9ecef;
          border-left: 4px solid #6c757d;
        }
        
        .message-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;
        }
        
        .message-sender {
          font-weight: 600;
          font-size: 14px;
        }
        
        .message-time {
          font-size: 12px;
          color: #666;
        }
        
        .message-text {
          line-height: 1.4;
          white-space: pre-wrap;
        }
        
        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #666;
          text-align: center;
        }
        
        .empty-state i {
          font-size: 48px;
          margin-bottom: 20px;
          opacity: 0.5;
        }
        
        .loading {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
        }
        
        .spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #f3f3f3;
          border-top: 4px solid #007bff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        .new-message-indicator {
          position: fixed;
          bottom: 80px;
          right: 50%;
          transform: translateX(50%);
          background: #28a745;
          color: white;
          padding: 12px 24px;
          border-radius: 25px;
          cursor: pointer;
          display: none;
          z-index: 1000;
          box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
          animation: slideUp 0.3s ease-out;
          transition: all 0.2s ease;
          font-weight: 500;
          font-size: 14px;
        }

        .new-message-indicator:hover {
          background: #218838;
          transform: translateX(50%) translateY(-2px);
          box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateX(50%) translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateX(50%) translateY(0);
          }
        }
        
        @media (max-width: 768px) {
          .sidebar {
            width: 250px;
          }
          
          .message-content {
            max-width: 85%;
          }
        }
      </style>
    </head>
    <body>
             <div class="sidebar">
         <div class="sidebar-header">
           <div class="online-indicator"></div>
           <h1><i class="fas fa-comments"></i> Live Chat</h1>
           <p>BlazingBoost Order Chats</p>
           <button id="notificationBtn" class="notification-btn" onclick="toggleNotifications()" style="display: none;">
             <i class="fas fa-bell"></i> Включить уведомления
           </button>
           <button id="testNotificationBtn" class="test-notification-btn" onclick="testNotification()" style="display: none;">
             <i class="fas fa-vial"></i> Тест уведомления
           </button>

           <!-- Кнопка выхода в правом нижнем углу header -->
           <a href="/logout" class="logout-btn" title="Выйти">
             <i class="fas fa-sign-out-alt"></i> Выход
           </a>
         </div>
         <div class="search-box">
           <input type="text" class="search-input" id="searchInput" placeholder="Поиск по Service UID или Chat ID...">
         </div>
         <div class="chat-list" id="chatList">
           <div class="loading">
             <div class="spinner"></div>
           </div>
         </div>
       </div>
      
      <div class="main-content">
        <div class="chat-header" id="chatHeader" style="display: none;">
          <div class="chat-info">
            <h2 id="chatTitle">Выберите чат</h2>
            <div class="chat-meta" id="chatMeta"></div>
          </div>
          <div class="chat-status">
            <div class="status-indicator"></div>
            <span>Live</span>
          </div>
        </div>
        
        <div class="messages-container" id="messagesContainer">
          <div class="empty-state">
            <i class="fas fa-comments"></i>
            <h3>Выберите чат для начала</h3>
            <p>Все активные чаты заказов отображаются в левой панели</p>
          </div>
        </div>
      </div>
      
      <div class="new-message-indicator" id="newMessageIndicator">
        <i class="fas fa-arrow-down"></i> Новые сообщения
      </div>

      <!-- Поле ввода сообщений -->
      <div class="message-input-container" id="messageInputContainer">
        <form class="message-input-form" id="messageForm">
          <textarea
            class="message-input"
            id="messageInput"
            placeholder="Введите сообщение..."
            rows="1"
            maxlength="1000"
          ></textarea>
          <button type="submit" class="send-button" id="sendButton" disabled>
            <i class="fas fa-paper-plane"></i>
          </button>
        </form>
      </div>

<script src="/socket.io/socket.io.js"></script>
<script src="/protected/client.js"></script>
    </body>
    </html>
  `);
});

// API для получения списка чатов с дополнительной информацией
app.get('/api/chats', requireAuth, async (req, res) => {
  try {
    // Получаем данные напрямую из API чатов
    const apiData = await getChatsFromAPI(30, 0); // Получаем больше чатов

    if (!apiData || !apiData.items) {
      return res.status(500).json({ error: 'Не удалось получить данные чатов из API' });
    }

    const chatArray = apiData.items.map(chat => {
      // Предзагружаем channelId если есть в данных чата
      if (chat.channelId) {
        chatChannels.set(chat.id, chat.channelId);
        console.log('📋 Предзагружен channelId из API для чата:', chat.id, '→', chat.channelId);
      } else if (chat.channel && chat.channel.id) {
        // Иногда channelId может быть в chat.channel.id
        chatChannels.set(chat.id, chat.channel.id);
        console.log('📋 Предзагружен channelId из channel.id для чата:', chat.id, '→', chat.channel.id);
      }

      return {
        chatId: chat.id,
        serviceName: chat.title,
        serviceUids: [chat.subtitle], // subtitle содержит Service UID
        lastMessage: chat.lastMessage ? {
          text: chat.lastMessage.text || '[без текста]',
          author: chat.lastMessage.author?.name || 'Система',
          timestamp: chat.lastMessage.timestamp,
          type: chat.lastMessage.author?.type === 'system' ? 'system-message' : 'user-message'
        } : null,
        lastActivity: chat.lastMessage?.timestamp || chat.timestamp,
        unreadCount: chat.unreadMessagesCount || 0,
        onlineStatus: chat.onlineStatus,
        chatType: chat.type,
        channel: chat.channel,
        channelId: chat.channelId || (chat.channel && chat.channel.id) || null
      };
    });

    // Сортируем по последней активности (новые сверху)
    chatArray.sort((a, b) => {
      const timeA = a.lastActivity || 0;
      const timeB = b.lastActivity || 0;
      return timeB - timeA;
    });

    console.log(`📋 Загружено ${chatArray.length} чатов, предзагружено ${chatChannels.size} channelId`);
    res.json(chatArray);
  } catch (error) {
    console.error('Ошибка получения чатов:', error);
    res.status(500).json({ error: error.message });
  }
});

// API для получения сообщений чата
app.get('/api/chat/:chatId', requireAuth, async (req, res) => {
  try {
    const { chatId } = req.params;
    const messages = await getChatMessages(chatId);
    
    if (!messages) {
      res.json({ error: 'Не удалось получить сообщения' });
      return;
    }
    
    res.json(messages);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// API для получения channelId чата
app.get('/api/chat/:chatId/channel', requireAuth, async (req, res) => {
  try {
    const { chatId } = req.params;
    let channelId = chatChannels.get(chatId);

    console.log('Запрос channelId для чата:', chatId);
    console.log('Всего сохранено channelId:', chatChannels.size);

    if (channelId) {
      console.log('✅ Найден сохраненный channelId:', channelId);
      res.json({ channelId, chatId });
    } else {
      console.log('❌ channelId не найден, пытаемся получить из API...');

      // Пытаемся найти channelId в свежих данных API
      try {
        const apiData = await getChatsFromAPI(100, 0);
        if (apiData && apiData.items) {
          const targetChat = apiData.items.find(chat => chat.id === chatId);
          if (targetChat) {
            channelId = targetChat.channelId || (targetChat.channel && targetChat.channel.id) || null;
            if (channelId) {
              chatChannels.set(chatId, channelId);
              console.log('✅ Получен channelId из API:', channelId);
              res.json({ channelId, chatId });
              return;
            }
          }
        }
      } catch (apiError) {
        console.warn('Ошибка получения данных из API:', apiError.message);
      }

      console.log('❌ channelId не найден нигде для чата:', chatId);
      res.json({ channelId: null, chatId, message: 'channelId не найден для этого чата' });
    }
  } catch (error) {
    console.error('Ошибка получения channelId:', error);
    res.status(500).json({ error: error.message });
  }
});

// API для поиска чата по Service UID
app.get('/api/search/:serviceUid', requireAuth, async (req, res) => {
  try {
    const { serviceUid } = req.params;
    const result = await findChatByServiceUid(serviceUid);

    if (result) {
      res.json(result);
    } else {
      res.json({ error: 'Чат не найден' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// API для отправки сообщений в чат
app.post('/api/send-message', requireAuth, async (req, res) => {
  try {
    const { chatId, text, channelId } = req.body;

    console.log('Получен запрос на отправку:', { chatId, channelId, textLength: text?.length });

    if ((!chatId && !channelId) || !text) {
      return res.status(400).json({ error: 'Требуется (chatId или channelId) и text' });
    }

    const CHAT_BEARER = process.env.CHAT_BEARER;
    if (!CHAT_BEARER) {
      return res.status(500).json({ error: 'CHAT_BEARER не настроен' });
    }

    // Убираем greetings запрос - отправляем только основное сообщение
    console.log('Пропускаем greetings, отправляем только сообщение');

    // Основной запрос - отправка сообщения

    // В оригинальном запросе передаются ОБА поля!
    // Определяем actualChannelId
    let actualChannelId = channelId && channelId !== 'fallback' ? channelId : null;

    // Если channelId не передан, пытаемся найти его в сохраненных данных
    if (!actualChannelId && chatId) {
      actualChannelId = chatChannels.get(chatId);
      console.log('Найден сохраненный channelId для чата', chatId, ':', actualChannelId);
    }

    // Если все еще нет channelId, используем fallback из примера
    if (!actualChannelId) {
      actualChannelId = 'a1a0414f4860eb4241fa29fb7885e3f3';
      console.log('Используем fallback channelId:', actualChannelId);
    }

    // Проверяем, что у нас есть chatId (обязательно)
    if (!chatId) {
      return res.status(400).json({ error: 'chatId обязателен для отправки сообщения' });
    }

    // Создаем raw multipart body точно как в оригинальном запросе
    const boundary = 'WebKitFormBoundaryMPMW1CGFPNsKIbsq'; // Boundary без префикса
    const textWithSpaces = text + ' \r\n\r\n'; // Добавляем пробел и переносы как в оригинале

    const rawBody = [
      `------${boundary}`,
      'Content-Disposition: form-data; name="channelId"',
      '',
      actualChannelId,
      `------${boundary}`,
      'Content-Disposition: form-data; name="chatId"',
      '',
      chatId,
      `------${boundary}`,
      'Content-Disposition: form-data; name="text"',
      '',
      textWithSpaces,
      `------${boundary}`,
      'Content-Disposition: form-data; name="mentions"',
      '',
      '',
      `------${boundary}--`
    ].join('\r\n');

    console.log(`✅ Raw multipart body создан:`);
    console.log(`   channelId: "${actualChannelId}"`);
    console.log(`   chatId: "${chatId}"`);
    console.log(`   text: "${textWithSpaces}" (с пробелами и переносами)`);
    console.log(`   mentions: "" (пустая строка)`);
    console.log(`   boundary: ${boundary}`);

    // Логируем первые 200 символов body для отладки
    console.log('📋 Первые 200 символов body:');
    console.log(rawBody.substring(0, 200) + '...');

    console.log(`🚀 Отправляем запрос к API с credentials: include...`);

    const response = await fetch('https://api.chat-service-blazing-group.com/v2/widget/messages/send', {
      method: 'POST',
      mode: 'cors',
      credentials: 'include', // Важно! Как в оригинальном запросе
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7',
        'authorization': `Bearer ${CHAT_BEARER}`,
        'content-type': `multipart/form-data; boundary=----${boundary}`,
        'priority': 'u=1, i',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'x-socket-id': req.headers['x-socket-id'] || '274382979.518377932'
      },
      referrer: 'https://blazingboost.com/', // referrer вместо Referer
      body: rawBody
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Ошибка отправки сообщения:', response.status, errorText);
      return res.status(response.status).json({ error: 'Ошибка отправки сообщения', details: errorText });
    }

    const result = await response.json();
    console.log('🎉 Сообщение успешно отправлено!');
    console.log(`   В чат: ${chatId}`);
    console.log(`   Через channelId: ${actualChannelId}`);
    console.log(`   Текст: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);
    res.json({ success: true, result });

  } catch (error) {
    console.error('Ошибка API отправки сообщения:', error);
    res.status(500).json({ error: error.message });
  }
});

// API для получения сообщений от monitor.js
app.post('/api/broadcast-message', express.json(), (req, res) => {
  try {
    const { chatId, message } = req.body;
    console.log('Получено сообщение от монитора для чата:', chatId);
    broadcastNewMessage(chatId, message);
    res.json({ success: true });
  } catch (error) {
    console.error('Ошибка обработки сообщения от монитора:', error);
    res.status(500).json({ error: error.message });
  }
});

// API для получения количества подключенных пользователей
app.get('/api/connected-users', (req, res) => {
  try {
    const connectedUsers = io.engine.clientsCount;
    res.json({
      connectedUsers,
      hasActiveUsers: connectedUsers > 0,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Ошибка получения количества пользователей:', error);
    res.status(500).json({ error: error.message });
  }
});

// API для отправки сообщения по Service ID (только localhost)
app.post('/api/send-by-service-id', requireLocalhost, express.json(), async (req, res) => {
  try {
    const { serviceId, text } = req.body;

    // Валидация входных данных
    if (!serviceId || !text) {
      return res.status(400).json({
        error: 'Требуются параметры serviceId и text',
        example: { serviceId: 'S25B50FA03', text: 'Ваше сообщение' }
      });
    }

    console.log(`📨 Запрос отправки сообщения по Service ID: ${serviceId}`);

    // Ищем чат по Service ID
    const chatInfo = await findChatByServiceUid(serviceId);
    if (!chatInfo) {
      console.warn(`❌ Чат не найден для Service ID: ${serviceId}`);
      return res.status(404).json({
        error: `Чат не найден для Service ID: ${serviceId}`
      });
    }

    const chatId = chatInfo.chatId;
    console.log(`✅ Найден чат ${chatId} для Service ID: ${serviceId}`);

    // Получаем Bearer токен
    const CHAT_BEARER = process.env.CHAT_BEARER;
    if (!CHAT_BEARER) {
      return res.status(500).json({ error: 'CHAT_BEARER не настроен' });
    }

    // Получаем channelId для отправки
    let channelId = chatInfo.channelId;
    if (!channelId) {
      // Пытаемся найти channelId в свежих данных API
      try {
        const apiData = await getChatsFromAPI(100, 0);
        if (apiData && apiData.items) {
          const targetChat = apiData.items.find(chat => chat.id === chatId);
          if (targetChat) {
            channelId = targetChat.channelId || (targetChat.channel && targetChat.channel.id) || null;
            console.log(`📡 Найден channelId из API: ${channelId}`);
          }
        }
      } catch (error) {
        console.warn('Ошибка получения channelId из API:', error.message);
      }
    }

    if (!channelId) {
      return res.status(500).json({
        error: `ChannelId не найден для чата ${chatId}`
      });
    }

    // Создаем raw multipart body точно как в рабочем /api/send-message
    const boundary = 'WebKitFormBoundaryMPMW1CGFPNsKIbsq'; // Boundary без префикса
    const textWithSpaces = text + ' \r\n\r\n'; // Добавляем пробел и переносы как в оригинале

    const rawBody = [
      `------${boundary}`,
      'Content-Disposition: form-data; name="channelId"',
      '',
      channelId,
      `------${boundary}`,
      'Content-Disposition: form-data; name="chatId"',
      '',
      chatId,
      `------${boundary}`,
      'Content-Disposition: form-data; name="text"',
      '',
      textWithSpaces,
      `------${boundary}`,
      'Content-Disposition: form-data; name="mentions"',
      '',
      '',
      `------${boundary}--`
    ].join('\r\n');

    // Отправляем сообщение
    const response = await fetch('https://api.chat-service-blazing-group.com/v2/widget/messages/send', {
      method: 'POST',
      mode: 'cors',
      credentials: 'include', // Важно! Как в оригинальном запросе
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7',
        'authorization': `Bearer ${CHAT_BEARER}`,
        'content-type': `multipart/form-data; boundary=----${boundary}`,
        'priority': 'u=1, i',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'x-socket-id': '274382979.518377932'
      },
      referrer: 'https://blazingboost.com/', // referrer вместо Referer
      body: rawBody
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Ошибка отправки сообщения: ${response.status}`, errorText);
      return res.status(response.status).json({
        error: 'Ошибка отправки сообщения',
        details: errorText
      });
    }

    const result = await response.json();
    console.log(`🎉 Сообщение успешно отправлено в чат ${chatId} (Service ID: ${serviceId})`);
    console.log(`   Текст: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);

    res.json({
      success: true,
      chatId,
      serviceId,
      channelId,
      result
    });

  } catch (error) {
    console.error('❌ Ошибка API отправки по Service ID:', error);
    res.status(500).json({ error: error.message });
  }
});

// WebSocket обработчики
io.on('connection', (socket) => {
  console.log('Клиент подключился:', socket.id);

  socket.on('join-chat', (chatId) => {
    socket.join(chatId);
    console.log('Клиент присоединился к чату:', chatId);
  });



  socket.on('disconnect', () => {
    console.log('Клиент отключился:', socket.id);
  });
});

// Функция для отправки новых сообщений через WebSocket
function broadcastNewMessage(chatId, message) {
  // Рассылаем всем клиентам, чтобы получать уведомления и обновлять список без подписок
  io.emit('new-message', { chatId, message });
}

// Экспортируем функцию для использования в других модулях
module.exports = { broadcastNewMessage };

server.listen(PORT, () => {
  console.log('Live Chat интерфейс доступен по адресу: http://localhost:' + PORT);
  console.log('Современный дизайн с WebSocket обновлениями');
  console.log('Адаптивный интерфейс для мобильных устройств');
  console.log('Автообновление списка чатов каждые 2 минуты');
  console.log('Автообновление активного чата каждые 30 секунд');
  console.log('Real-time обновления через Pusher WebSocket');
  console.log('Для остановки нажмите Ctrl+C');
});
