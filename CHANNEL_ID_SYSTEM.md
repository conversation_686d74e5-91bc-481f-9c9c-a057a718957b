# 🔗 Система передачи channelId

## ✅ Что реализовано:

### 1. **Передача channelId из монитора**
- Monitor.js теперь извлекает `channelId` из сообщений
- Передает `channelId` в веб-интерфейс через API
- Логирует `channelId` для отладки

### 2. **Хранение channelId в веб-интерфейсе**
- Веб-сервер сохраняет `channelId` для каждого чата
- Использует Map для быстрого доступа: `chatId → channelId`
- API endpoint для получения `channelId` по `chatId`

### 3. **Использование channelId при отправке**
- Автоматически получает `channelId` при выборе чата
- Отправляет ОБА поля: `channelId` и `chatId` (как в оригинале)
- Fallback на дефолтный `channelId` если не найден

## 🔄 **Поток данных:**

```
1. Monitor получает сообщение с channelId
   ↓
2. Monitor отправляет в веб-интерфейс: {chatId, message, channelId}
   ↓
3. Веб-сервер сохраняет: chatChannels.set(chatId, channelId)
   ↓
4. При выборе чата клиент запрашивает: GET /api/chat/{chatId}/channel
   ↓
5. При отправке сообщения используется сохраненный channelId
```

## 🎯 **API Endpoints:**

### **Получение channelId:**
```javascript
GET /api/chat/{chatId}/channel
Response: {
  "channelId": "a1a0414f4860eb4241fa29fb7885e3f3",
  "chatId": "f91e845d70135b717755826757ec20ca"
}
```

### **Отправка сообщения:**
```javascript
POST /api/send-message
Body: {
  "chatId": "f91e845d70135b717755826757ec20ca",
  "text": "Привет!"
}
// channelId автоматически добавляется из сохраненных данных
```

## 🔧 **Логирование для отладки:**

### **В monitor.js:**
```
Сообщение отправлено в веб-интерфейс для чата: abc123 (channelId: def456)
```

### **В chat-web.js:**
```
Сохранен channelId для чата: abc123 → def456
Найден сохраненный channelId для чата abc123 : def456
FormData создан с полями: channelId=def456, chatId=abc123, text="👍", mentions=""
```

### **В client.js:**
```
Получен channelId для чата: abc123 → def456
Отправляем через channelId: def456
```

## 🚀 **Для тестирования:**

1. **Перезапустите оба сервиса:**
   ```bash
   # Терминал 1
   npm run monitor
   
   # Терминал 2  
   npm run chat-web
   ```

2. **Дождитесь сообщений в мониторе:**
   - Monitor должен получить сообщения с channelId
   - Веб-интерфейс должен сохранить channelId

3. **Попробуйте отправить сообщение:**
   - Выберите чат в веб-интерфейсе
   - Отправьте тестовое сообщение
   - Проверьте логи на наличие channelId

## ⚠️ **Fallback система:**

Если channelId не найден:
1. Используется дефолтный: `a1a0414f4860eb4241fa29fb7885e3f3`
2. Логируется предупреждение
3. Отправка все равно работает

## 🎯 **Ожидаемый результат:**

Теперь отправка сообщений должна работать с правильными `channelId` и `chatId`, как в оригинальном запросе!
